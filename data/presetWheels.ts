import type { PresetWheel } from '~/types'

export const presetWheels: PresetWheel[] = [
  {
    id: 'yes-no',
    name: 'Yes or No Decision Maker',
    slug: 'yes-no-decision-maker',
    isDefault: true,
    prizes: [
      { text: "Yes", color: "#22c55e" },
      { text: "No", color: "#ef4444" },
    ],
    seo: {
      title: 'Yes or No Decision Maker - Quick Binary Choice Wheel',
      description: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties.',
      keywords: ['yes no decision', 'binary choice', 'decision maker', 'quick decision']
    },
    articles: [
      {
        title: "The Psychology of Binary Decisions",
        content: `
          <p>
            Binary decisions are fundamental to human psychology. When faced with a simple yes or no choice, 
            our brains often overthink the process, leading to decision paralysis.
          </p>
          <p>
            Using a decision wheel removes the burden of choice from your conscious mind, allowing your 
            subconscious preferences to guide the outcome. This can often lead to more satisfying decisions.
          </p>
        `
      },
      {
        title: "When to Use Yes/No Decision Making",
        content: `
          <p>
            Yes or no decisions are perfect for:
          </p>
          <ul>
            <li>Breaking ties when you're genuinely undecided</li>
            <li>Making quick decisions when time is limited</li>
            <li>Overcoming analysis paralysis</li>
            <li>Adding an element of fun to routine choices</li>
          </ul>
        `
      }
    ]
  },
  {
    id: 'what-to-eat',
    name: 'What to Eat Decision Wheel',
    slug: 'what-to-eat-decision-wheel',
    prizes: [
      { text: "Pizza", color: "#ff6b6b" },
      { text: "Burger", color: "#4ecdc4" },
      { text: "Sushi", color: "#45b7d1" },
      { text: "Pasta", color: "#96ceb4" },
      { text: "Tacos", color: "#feca57" },
      { text: "Salad", color: "#ff9ff3" },
      { text: "Sandwich", color: "#54a0ff" },
      { text: "Chinese", color: "#5f27cd" },
    ],
    seo: {
      title: 'What to Eat Decision Wheel - Food Choice Generator',
      description: 'Cant decide what to eat? Use our food decision wheel to randomly choose from popular meal options. Perfect for indecisive foodies!',
      keywords: ['what to eat', 'food decision', 'meal picker', 'restaurant choice', 'food wheel']
    },
    articles: [
      {
        title: "Solving the Daily Food Dilemma",
        content: `
          <p>
            "What should I eat?" is one of the most common daily decisions we face. Decision fatigue from 
            constantly choosing meals can be exhausting.
          </p>
          <p>
            Our food decision wheel takes the stress out of meal planning by randomly selecting from 
            popular food options, helping you discover new favorites and break out of eating routines.
          </p>
        `
      }
    ]
  },
  {
    id: 'movie-night',
    name: 'Movie Night Picker',
    slug: 'movie-night-picker',
    prizes: [
      { text: "Action", color: "#e74c3c" },
      { text: "Comedy", color: "#f39c12" },
      { text: "Drama", color: "#9b59b6" },
      { text: "Horror", color: "#2c3e50" },
      { text: "Romance", color: "#e91e63" },
      { text: "Sci-Fi", color: "#3498db" },
      { text: "Thriller", color: "#34495e" },
      { text: "Documentary", color: "#27ae60" },
    ],
    seo: {
      title: 'Movie Night Picker - Random Movie Genre Selector',
      description: 'End movie night arguments with our genre picker wheel. Randomly select from popular movie genres for your next film night.',
      keywords: ['movie picker', 'film genre', 'movie night', 'what to watch', 'movie decision']
    },
    articles: [
      {
        title: "Making Movie Night Decisions Fair",
        content: `
          <p>
            Movie night with friends or family often leads to lengthy debates about what to watch. 
            Different people have different preferences, and finding a compromise can be challenging.
          </p>
          <p>
            Using a movie genre picker wheel ensures everyone has an equal chance of their preferred 
            genre being selected, making the decision process fair and fun.
          </p>
        `
      }
    ]
  },
  {
    id: 'weekend-activity',
    name: 'Weekend Activity Planner',
    slug: 'weekend-activity-planner',
    prizes: [
      { text: "Hiking", color: "#2ecc71" },
      { text: "Beach", color: "#3498db" },
      { text: "Museum", color: "#9b59b6" },
      { text: "Shopping", color: "#e91e63" },
      { text: "Park", color: "#27ae60" },
      { text: "Movies", color: "#34495e" },
      { text: "Restaurant", color: "#e67e22" },
      { text: "Stay Home", color: "#95a5a6" },
    ],
    seo: {
      title: 'Weekend Activity Planner - Random Activity Generator',
      description: 'Plan your perfect weekend with our activity picker wheel. Discover new activities and break out of your routine.',
      keywords: ['weekend activities', 'activity planner', 'what to do', 'weekend ideas', 'activity picker']
    },
    articles: [
      {
        title: "Breaking Out of Weekend Routines",
        content: `
          <p>
            Many people fall into predictable weekend routines, doing the same activities week after week. 
            While routine can be comforting, it can also lead to boredom and missed opportunities.
          </p>
          <p>
            Our weekend activity planner introduces an element of randomness to your leisure time, 
            encouraging you to try new experiences and make the most of your free time.
          </p>
        `
      }
    ]
  },
  {
    id: 'team-picker',
    name: 'Team Member Picker',
    slug: 'team-member-picker',
    prizes: [
      { text: "Alice", color: "#ff7675" },
      { text: "Bob", color: "#74b9ff" },
      { text: "Charlie", color: "#00b894" },
      { text: "Diana", color: "#fdcb6e" },
      { text: "Eve", color: "#e17055" },
      { text: "Frank", color: "#a29bfe" },
    ],
    seo: {
      title: 'Team Member Picker - Random Team Selection Wheel',
      description: 'Fairly select team members, assign tasks, or pick volunteers with our random team picker wheel. Perfect for classrooms and workplaces.',
      keywords: ['team picker', 'random selection', 'team member selector', 'fair selection', 'group picker']
    },
    articles: [
      {
        title: "Fair Team Selection Made Easy",
        content: `
          <p>
            Whether you're a teacher assigning classroom tasks, a manager distributing work, or organizing team activities, 
            fair selection is crucial for maintaining group harmony.
          </p>
          <p>
            Our team picker wheel ensures everyone has an equal chance of being selected, removing bias and 
            making the process transparent and fun.
          </p>
        `
      }
    ]
  }
];

// 根据slug查找转盘
export function findWheelBySlug(slug: string): PresetWheel | undefined {
  return presetWheels.find(wheel => wheel.slug === slug);
}

// 获取默认转盘
export function getDefaultWheel(): PresetWheel {
  return presetWheels.find(wheel => wheel.isDefault) || presetWheels[0];
}

// 获取转盘列表
export function getWheelList() {
  return presetWheels.map(wheel => ({
    id: wheel.id,
    name: wheel.name,
    slug: wheel.slug,
    description: wheel.seo.description
  }));
}
