export interface Prize {
  text: string;
  color: string;
  image?: string;
}

export interface LuckWheelProps {
  prizes: Prize[];
  size?: number;
  startButtonSize?: number;
  onPrizesChange?: (prizes: Prize[]) => void;
  wheelId?: string; // 用于区分不同页面的localStorage
  title?: string; // 转盘标题，用于全屏模式显示
}

// 预设转盘的SEO信息
export interface WheelSEO {
  title: string;
  description: string;
  keywords?: string[];
}

// 预设转盘的文章内容
export interface WheelArticle {
  title: string;
  content: string;
}

// 预设转盘数据结构
export interface PresetWheel {
  id: string;
  name: string;
  slug: string; // SEO友好的URL路径
  prizes: Prize[];
  seo: WheelSEO;
  articles: WheelArticle[];
  isDefault?: boolean; // 是否为默认转盘
}

// 转盘列表项（用于导航显示）
export interface WheelListItem {
  id: string;
  name: string;
  slug: string;
  description: string;
}

// 统一接口响应类型
export interface WheelPageResponse {
  currentWheel: PresetWheel;
  otherWheels: WheelListItem[];
  success: boolean;
  message?: string;
}

// 转盘列表接口响应类型
export interface WheelListResponse {
  wheels: WheelListItem[];
  total: number;
  success: boolean;
  message?: string;
}