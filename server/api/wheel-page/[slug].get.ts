/**
 * 统一的转盘页面接口
 * 返回当前转盘信息和其他转盘列表
 * 
 * GET /api/wheel-page/[slug]
 */

import type { WheelPageResponse } from '~/types'
import { findWheelBySlug, getWheelList } from '~/data/presetWheels'
import { handleServerError, createApiResponse, getCacheHeaders } from '~/utils/server-api'

export default defineEventHandler(async (event) => {
  try {
    // 获取路由参数
    const slug = getRouterParam(event, 'slug')
    
    if (!slug) {
      setResponseStatus(event, 400)
      return {
        currentWheel: null,
        otherWheels: [],
        success: false,
        message: 'Slug parameter is required'
      } as WheelPageResponse
    }

    // 设置缓存头
    setHeaders(event, getCacheHeaders(3600)) // 1小时缓存
    
    // 获取当前转盘信息
    const currentWheel = findWheelBySlug(slug)
    
    if (!currentWheel) {
      setResponseStatus(event, 404)
      
      const response: WheelPageResponse = {
        currentWheel: null,
        otherWheels: [],
        success: false,
        message: `Wheel with slug "${slug}" not found`
      }
      
      return response
    }
    
    // 获取所有转盘列表，并过滤掉当前转盘
    const allWheels = getWheelList()
    const otherWheels = allWheels.filter(wheel => wheel.slug !== slug)
    
    const response: WheelPageResponse = {
      currentWheel,
      otherWheels,
      success: true,
      message: 'Wheel data retrieved successfully'
    }

    return response
  } catch (error) {
    console.error('Error in wheel-page/[slug] API:', error)
    
    const errorResponse = handleServerError(error, 'wheel-page/[slug] API')
    
    setResponseStatus(event, errorResponse.statusCode)
    
    const response: WheelPageResponse = {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: errorResponse.message
    }

    return response
  }
})
