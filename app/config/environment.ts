/**
 * 环境配置管理器
 * 统一管理所有环境相关的配置
 */

// 环境类型定义
export type Environment = 'development' | 'production' | 'test' | 'staging';

// 配置接口定义
interface EnvironmentConfig {
  apiBaseUrl: string;
  siteUrl: string;
  isDevelopment: boolean;
  isProduction: boolean;
  isTest: boolean;
  environment: Environment;
  useMockApi: boolean;
}

/**
 * 获取当前环境
 */
export function getCurrentEnvironment(): Environment {
  // 优先级：NODE_ENV > NEXT_PUBLIC_ENV > 默认值
  const nodeEnv = process.env.NODE_ENV as Environment;
  const nextEnv = process.env.NEXT_PUBLIC_ENV as Environment;
  
  return nextEnv || nodeEnv || 'development';
}

/**
 * 检查是否为特定环境
 */
export function isEnvironment(env: Environment): boolean {
  return getCurrentEnvironment() === env;
}

/**
 * 获取环境配置
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const environment = getCurrentEnvironment();
  const isDevelopment = environment === 'development';
  const isProduction = environment === 'production';
  const isTest = environment === 'test';
  
  // API基础URL配置
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || getDefaultApiUrl(environment);
  
  // 网站URL配置
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || getDefaultSiteUrl(environment);
  
  // 判断是否使用Mock API
  const useMockApi = apiBaseUrl === '/api' || apiBaseUrl.includes('localhost:3000');
  
  return {
    apiBaseUrl,
    siteUrl,
    isDevelopment,
    isProduction,
    isTest,
    environment,
    useMockApi,
  };
}

/**
 * 获取默认API URL
 */
function getDefaultApiUrl(environment: Environment): string {
  switch (environment) {
    case 'development':
      return '/api'; // 使用Mock API
    case 'test':
      return 'https://test-api.yourdomain.com/v1';
    case 'staging':
      return 'https://staging-api.yourdomain.com/v1';
    case 'production':
      return 'https://api.yourdomain.com/v1';
    default:
      return '/api';
  }
}

/**
 * 获取默认网站URL
 */
function getDefaultSiteUrl(environment: Environment): string {
  switch (environment) {
    case 'development':
      return 'http://localhost:3000';
    case 'test':
      return 'https://test.yourdomain.com';
    case 'staging':
      return 'https://staging.yourdomain.com';
    case 'production':
      return 'https://yourdomain.com';
    default:
      return 'http://localhost:3000';
  }
}

/**
 * 验证环境配置
 */
export function validateEnvironmentConfig(): { isValid: boolean; errors: string[] } {
  const config = getEnvironmentConfig();
  const errors: string[] = [];
  
  // 检查API URL
  if (!config.apiBaseUrl) {
    errors.push('API Base URL is not configured');
  }
  
  // 检查网站URL
  if (!config.siteUrl) {
    errors.push('Site URL is not configured');
  }
  
  // 生产环境特殊检查
  if (config.isProduction) {
    if (config.useMockApi) {
      errors.push('Production environment should not use Mock API');
    }
    
    if (config.siteUrl.includes('localhost') || config.siteUrl.includes('yourdomain.com')) {
      errors.push('Production environment should have real domain configured');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 打印环境配置信息
 */
export function logEnvironmentConfig(): void {
  const config = getEnvironmentConfig();
  const validation = validateEnvironmentConfig();
  
  console.log('🌍 Environment Configuration:');
  console.log('============================');
  console.log(`Environment: ${config.environment}`);
  console.log(`API Base URL: ${config.apiBaseUrl}`);
  console.log(`Site URL: ${config.siteUrl}`);
  console.log(`Using Mock API: ${config.useMockApi ? 'Yes' : 'No'}`);
  
  if (!validation.isValid) {
    console.warn('⚠️ Configuration Issues:');
    validation.errors.forEach(error => console.warn(`  - ${error}`));
  } else {
    console.log('✅ Configuration is valid');
  }
}

/**
 * 获取特定环境的配置示例
 */
export function getEnvironmentConfigExample(environment: Environment): string {
  const apiUrl = getDefaultApiUrl(environment);
  const siteUrl = getDefaultSiteUrl(environment);
  
  return `# ${environment.toUpperCase()} Environment Configuration
NEXT_PUBLIC_ENV=${environment}
NEXT_PUBLIC_API_BASE_URL=${apiUrl}
NEXT_PUBLIC_SITE_URL=${siteUrl}

# Optional: Additional configuration
# NEXT_PUBLIC_API_TOKEN=your_api_token_here
# NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id_here
`;
}

// 导出配置实例
export const config = getEnvironmentConfig();

// 在开发环境下自动打印配置信息
if (config.isDevelopment && typeof window === 'undefined') {
  logEnvironmentConfig();
}
