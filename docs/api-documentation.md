# 转盘应用 API 文档

## 概述

本文档描述了转盘应用的统一 Mock API 接口，这些接口设计为方便后续接入真实后端服务。

## 基础 URL

```
http://localhost:3000/api
```

## 接口列表

### 1. 获取转盘页面数据

**接口地址：** `GET /wheel-page/{slug}`

**描述：** 获取指定转盘的完整页面数据，包括当前转盘信息和其他转盘列表

**参数：**
- `slug` (string, required): 转盘的 URL slug

**响应格式：**
```typescript
interface WheelPageResponse {
  currentWheel: PresetWheel;     // 当前转盘的完整信息
  otherWheels: WheelListItem[];  // 其他转盘的列表信息
  success: boolean;              // 请求是否成功
  message?: string;              // 响应消息
}
```

**示例请求：**
```bash
GET /api/wheel-page/what-to-eat-decision-wheel
```

**成功响应 (200)：**
```json
{
  "currentWheel": {
    "id": "what-to-eat",
    "name": "What to Eat Decision Wheel",
    "slug": "what-to-eat-decision-wheel",
    "prizes": [
      { "text": "Pizza", "color": "#ff6b6b" },
      { "text": "Chinese Food", "color": "#4ecdc4" }
    ],
    "seo": {
      "title": "What to Eat Decision Wheel - Food Choice Spinner",
      "description": "Can't decide what to eat? Use our food decision wheel...",
      "keywords": ["what to eat", "food decision"]
    },
    "articles": [
      {
        "title": "Solving the Daily 'What to Eat' Dilemma",
        "content": "<p>What should we eat?...</p>"
      }
    ]
  },
  "otherWheels": [
    {
      "id": "yes-no",
      "name": "Yes or No Decision Maker",
      "slug": "yes-no-decision-maker",
      "description": "Make quick yes or no decisions..."
    }
  ],
  "success": true,
  "message": "Wheel data retrieved successfully"
}
```

**错误响应 (404)：**
```json
{
  "currentWheel": null,
  "otherWheels": [],
  "success": false,
  "message": "Wheel with slug \"non-existent\" not found"
}
```

### 2. 获取转盘列表

**接口地址：** `GET /wheel-list`

**描述：** 获取所有可用转盘的列表信息

**响应格式：**
```typescript
interface WheelListResponse {
  wheels: WheelListItem[];  // 转盘列表
  total: number;            // 转盘总数
  success: boolean;         // 请求是否成功
  message?: string;         // 响应消息
}
```

**示例请求：**
```bash
GET /api/wheel-list
```

**成功响应 (200)：**
```json
{
  "wheels": [
    {
      "id": "yes-no",
      "name": "Yes or No Decision Maker",
      "slug": "yes-no-decision-maker",
      "description": "Make quick yes or no decisions with our simple decision wheel..."
    },
    {
      "id": "what-to-eat",
      "name": "What to Eat Decision Wheel",
      "slug": "what-to-eat-decision-wheel",
      "description": "Can't decide what to eat? Use our food decision wheel..."
    }
  ],
  "total": 4,
  "success": true,
  "message": "Wheel list retrieved successfully"
}
```

## 数据类型定义

### PresetWheel
```typescript
interface PresetWheel {
  id: string;
  name: string;
  slug: string;
  prizes: Prize[];
  seo: WheelSEO;
  articles: WheelArticle[];
  isDefault?: boolean;
}
```

### Prize
```typescript
interface Prize {
  text: string;
  color: string;
  image?: string;
}
```

### WheelSEO
```typescript
interface WheelSEO {
  title: string;
  description: string;
  keywords?: string[];
}
```

### WheelArticle
```typescript
interface WheelArticle {
  title: string;
  content: string;
}
```

### WheelListItem
```typescript
interface WheelListItem {
  id: string;
  name: string;
  slug: string;
  description: string;
}
```

## 使用示例

### 在 React 组件中使用

```typescript
// 获取转盘页面数据
const fetchWheelPageData = async (slug: string) => {
  try {
    const response = await fetch(`/api/wheel-page/${slug}`);
    const data: WheelPageResponse = await response.json();
    
    if (data.success) {
      // 使用 data.currentWheel 和 data.otherWheels
      console.log('Current wheel:', data.currentWheel);
      console.log('Other wheels:', data.otherWheels);
    } else {
      console.error('Error:', data.message);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};

// 获取转盘列表
const fetchWheelList = async () => {
  try {
    const response = await fetch('/api/wheel-list');
    const data: WheelListResponse = await response.json();
    
    if (data.success) {
      console.log('Wheels:', data.wheels);
      console.log('Total:', data.total);
    }
  } catch (error) {
    console.error('Error:', error);
  }
};
```

## 后续接入真实接口

当需要接入真实后端接口时，只需要：

1. 修改接口 URL 为真实的后端地址
2. 确保后端返回的数据格式与这里定义的接口格式一致
3. 根据需要添加认证头或其他请求参数

这样可以确保前端代码无需大幅修改即可切换到真实接口。
