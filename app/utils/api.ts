import { WheelPageResponse } from '@/app/components/wheel/types';
import { endpoints, requestConfig, getApiUrl, logApiCall } from '@/app/config/api';

/**
 * API 工具函数
 * 提供统一的接口调用方法，方便后续切换到真实后端接口
 */

/**
 * 通用的 fetch 封装
 */
async function apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {
  const url = getApiUrl(endpoint);

  // 记录API调用日志
  logApiCall(endpoint, options?.method || 'GET');

  const defaultOptions: RequestInit = {
    headers: {
      ...requestConfig.defaultHeaders,
      // 这里可以添加认证头等
      // 'Authorization': `Bearer ${token}`,
    },
    ...options,
  };

  const response = await fetch(url, defaultOptions);

  if (!response.ok) {
    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取转盘页面数据（包含当前转盘和其他转盘列表）
 * @param slug 转盘的 URL slug，为空时获取默认转盘
 * @returns Promise<WheelPageResponse>
 */
export async function getWheelPageData(slug?: string): Promise<WheelPageResponse> {
  return apiRequest<WheelPageResponse>(endpoints.wheelPage(slug));
}

/**
 * React Hook 风格的 API 调用工具
 */
export const useApi = {
  /**
   * 获取转盘页面数据的 Hook 风格函数
   */
  async wheelPageData(slug?: string) {
    try {
      const data = await getWheelPageData(slug);
      return { data, error: null, loading: false };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error', 
        loading: false 
      };
    }
  },


};

/**
 * 配置 API 基础 URL（用于切换到真实后端）
 * @deprecated 请使用环境变量配置API URL
 */
export function configureApiBaseUrl() {
  // 在实际应用中，这可能需要更复杂的配置管理
  console.warn('API base URL configuration should be done through environment variables');
}

/**
 * 示例：如何在组件中使用
 * 
 * ```typescript
 * import { getWheelPageData, getWheelList } from '@/app/utils/api';
 * 
 * // 在组件中使用
 * const MyComponent = ({ slug }: { slug: string }) => {
 *   const [wheelData, setWheelData] = useState(null);
 *   const [loading, setLoading] = useState(true);
 *   const [error, setError] = useState(null);
 * 
 *   useEffect(() => {
 *     const fetchData = async () => {
 *       try {
 *         const data = await getWheelPageData(slug);
 *         if (data.success) {
 *           setWheelData(data);
 *         } else {
 *           setError(data.message);
 *         }
 *       } catch (err) {
 *         setError(err.message);
 *       } finally {
 *         setLoading(false);
 *       }
 *     };
 * 
 *     fetchData();
 *   }, [slug]);
 * 
 *   if (loading) return <div>Loading...</div>;
 *   if (error) return <div>Error: {error}</div>;
 *   
 *   return (
 *     <div>
 *       <h1>{wheelData.currentWheel.name}</h1>
 *       // ... 其他组件内容
 *     </div>
 *   );
 * };
 * ```
 */
