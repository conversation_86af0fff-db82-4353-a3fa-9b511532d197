import { MetadataRoute } from 'next';
import { presetWheels } from '@/app/data/presetWheels';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://yourdomain.com'; // 替换为你的实际域名
  
  // 主页
  const routes: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 1,
    },
  ];

  // 添加所有预设转盘页面
  presetWheels.forEach((wheel) => {
    routes.push({
      url: `${baseUrl}/${wheel.slug}`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    });
  });

  return routes;
}
