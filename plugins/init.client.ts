/**
 * 客户端初始化插件
 */

export default defineNuxtPlugin(() => {
  // 初始化应用
  console.log('Nuxt app initialized on client')

  // 初始化主题
  const colorMode = useColorMode()
  
  // 确保主题正确应用
  if (process.client) {
    // 检查系统主题偏好
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    // 如果用户偏好是系统主题，监听系统主题变化
    if (colorMode.preference === 'system') {
      const updateTheme = () => {
        colorMode.value = mediaQuery.matches ? 'dark' : 'light'
      }
      
      // 初始设置
      updateTheme()
      
      // 监听变化
      mediaQuery.addEventListener('change', updateTheme)
      
      // 清理函数
      onBeforeUnmount(() => {
        mediaQuery.removeEventListener('change', updateTheme)
      })
    }
  }

  // 初始化分析
  if (process.client) {
    const runtimeConfig = useRuntimeConfig()
    
    // Google Analytics
    if (runtimeConfig.public.gtag) {
      // gtag 已在 nuxt.config.ts 中配置
      console.log('Google Analytics initialized')
    }
    
    // Baidu Analytics
    if (runtimeConfig.public.baiduAnalytics) {
      // Baidu Analytics 已在 nuxt.config.ts 中配置
      console.log('Baidu Analytics initialized')
    }
  }

  // 初始化错误处理
  const handleGlobalError = (error: any) => {
    console.error('Global error:', error)
    
    // 在生产环境中，可以将错误发送到错误监控服务
    if (process.env.NODE_ENV === 'production') {
      // 发送错误到监控服务
      // sendErrorToMonitoring(error)
    }
  }

  // 监听未捕获的错误
  if (process.client) {
    window.addEventListener('error', handleGlobalError)
    window.addEventListener('unhandledrejection', (event) => {
      handleGlobalError(event.reason)
    })
  }

  // 性能监控
  if (process.client && 'performance' in window) {
    // 监控页面加载性能
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (perfData) {
          const loadTime = perfData.loadEventEnd - perfData.loadEventStart
          const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart
          
          console.log('Performance metrics:', {
            loadTime,
            domContentLoaded,
            totalTime: perfData.loadEventEnd - perfData.fetchStart
          })
          
          // 在生产环境中，可以将性能数据发送到分析服务
          if (process.env.NODE_ENV === 'production') {
            // sendPerformanceData({ loadTime, domContentLoaded })
          }
        }
      }, 0)
    })
  }

  // 初始化用户偏好
  if (process.client) {
    // 从 localStorage 恢复用户设置
    const savedPreferences = localStorage.getItem('user-preferences')
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences)
        console.log('User preferences loaded:', preferences)
        
        // 应用用户偏好设置
        // applyUserPreferences(preferences)
      } catch (error) {
        console.warn('Failed to parse user preferences:', error)
        localStorage.removeItem('user-preferences')
      }
    }
  }

  // 初始化键盘快捷键
  if (process.client) {
    const handleKeydown = (event: KeyboardEvent) => {
      // 空格键启动轮盘
      if (event.code === 'Space' && !event.ctrlKey && !event.metaKey && !event.altKey) {
        const activeElement = document.activeElement
        
        // 如果焦点不在输入框上，触发轮盘旋转
        if (activeElement?.tagName !== 'INPUT' && activeElement?.tagName !== 'TEXTAREA') {
          event.preventDefault()
          
          // 查找轮盘的开始按钮并点击
          const startButton = document.querySelector('.startButton') as HTMLButtonElement
          if (startButton && !startButton.disabled) {
            startButton.click()
          }
        }
      }
      
      // Escape 键关闭设置面板
      if (event.code === 'Escape') {
        const settingsPanel = document.querySelector('.settingsPanel.open')
        if (settingsPanel) {
          const closeButton = settingsPanel.querySelector('button') as HTMLButtonElement
          closeButton?.click()
        }
      }
    }

    document.addEventListener('keydown', handleKeydown)
  }

  // 初始化触摸手势（移动端）
  if (process.client && 'ontouchstart' in window) {
    let touchStartY = 0
    let touchStartX = 0

    const handleTouchStart = (event: TouchEvent) => {
      touchStartY = event.touches[0].clientY
      touchStartX = event.touches[0].clientX
    }

    const handleTouchEnd = (event: TouchEvent) => {
      const touchEndY = event.changedTouches[0].clientY
      const touchEndX = event.changedTouches[0].clientX
      
      const deltaY = touchStartY - touchEndY
      const deltaX = touchStartX - touchEndX
      
      // 检测向上滑动手势（显示设置）
      if (Math.abs(deltaY) > Math.abs(deltaX) && deltaY > 50) {
        const settingsButton = document.querySelector('.settingsButton') as HTMLButtonElement
        if (settingsButton) {
          settingsButton.click()
        }
      }
    }

    document.addEventListener('touchstart', handleTouchStart, { passive: true })
    document.addEventListener('touchend', handleTouchEnd, { passive: true })
  }

  // 返回插件提供的功能
  return {
    provide: {
      // 提供全局方法
      trackEvent: (eventName: string, parameters?: Record<string, any>) => {
        if (process.client && window.gtag) {
          window.gtag('event', eventName, parameters)
        }
      },
      
      showNotification: (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
        // 简单的通知实现
        console.log(`[${type.toUpperCase()}] ${message}`)
        
        // 在实际应用中，这里可以显示 toast 通知
        // showToast(message, type)
      }
    }
  }
})
