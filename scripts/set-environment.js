#!/usr/bin/env node

/**
 * 环境切换脚本
 * 用于快速切换不同的环境配置
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 环境配置模板
const environmentConfigs = {
  development: {
    NEXT_PUBLIC_ENV: 'development',
    NEXT_PUBLIC_API_BASE_URL: '/api',
    NEXT_PUBLIC_SITE_URL: 'http://localhost:3000'
  },
  'development-real': {
    NEXT_PUBLIC_ENV: 'development',
    NEXT_PUBLIC_API_BASE_URL: 'http://admin.makechoice.local',
    NEXT_PUBLIC_SITE_URL: 'http://localhost:3000'
  },
  staging: {
    NEXT_PUBLIC_ENV: 'staging',
    NEXT_PUBLIC_API_BASE_URL: 'https://staging-api.yourdomain.com/v1',
    NEXT_PUBLIC_SITE_URL: 'https://staging.yourdomain.com'
  },
  production: {
    NEXT_PUBLIC_ENV: 'production',
    NEXT_PUBLIC_API_BASE_URL: 'https://api.yourdomain.com/v1',
    NEXT_PUBLIC_SITE_URL: 'https://yourdomain.com'
  }
};

function generateEnvContent(config) {
  let content = `# Environment Configuration\n`;
  content += `# Generated on ${new Date().toISOString()}\n\n`;
  
  Object.entries(config).forEach(([key, value]) => {
    content += `${key}=${value}\n`;
  });
  
  content += `\n# Optional: Additional configuration\n`;
  content += `# NEXT_PUBLIC_API_TOKEN=your_api_token_here\n`;
  content += `# NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id_here\n`;
  
  return content;
}

function setEnvironment(envName) {
  const config = environmentConfigs[envName];
  
  if (!config) {
    log(`❌ 未知环境: ${envName}`, 'red');
    log('可用环境:', 'yellow');
    Object.keys(environmentConfigs).forEach(env => {
      log(`  - ${env}`, 'blue');
    });
    return;
  }
  
  const envPath = path.join(process.cwd(), '.env.local');
  const envContent = generateEnvContent(config);
  
  try {
    // 备份现有配置
    if (fs.existsSync(envPath)) {
      const backupPath = `${envPath}.backup.${Date.now()}`;
      fs.copyFileSync(envPath, backupPath);
      log(`📦 已备份现有配置到: ${path.basename(backupPath)}`, 'yellow');
    }
    
    // 写入新配置
    fs.writeFileSync(envPath, envContent);
    log(`✅ 已切换到 ${envName} 环境`, 'green');
    log('📝 配置内容:', 'blue');
    console.log(envContent);
    
    // 显示使用说明
    log('🔄 请重启开发服务器以应用新配置:', 'yellow');
    log('   npm run dev', 'blue');
    
  } catch (error) {
    log(`❌ 写入配置文件失败: ${error.message}`, 'red');
  }
}

function showCurrentEnvironment() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    log('❌ 未找到 .env.local 文件', 'red');
    return;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  log('📋 当前环境配置:', 'blue');
  console.log(envContent);
}

function showHelp() {
  log('🔧 环境切换工具', 'blue');
  log('================', 'blue');
  log('用法:', 'yellow');
  log('  npm run set-env <environment>', 'blue');
  log('  npm run set-env current', 'blue');
  log('  npm run set-env help', 'blue');
  log('');
  log('可用环境:', 'yellow');
  Object.entries(environmentConfigs).forEach(([env, config]) => {
    log(`  ${env}:`, 'blue');
    log(`    API: ${config.NEXT_PUBLIC_API_BASE_URL}`, 'reset');
    log(`    Site: ${config.NEXT_PUBLIC_SITE_URL}`, 'reset');
  });
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command || command === 'help') {
    showHelp();
    return;
  }
  
  if (command === 'current') {
    showCurrentEnvironment();
    return;
  }
  
  setEnvironment(command);
}

// 运行脚本
main();
