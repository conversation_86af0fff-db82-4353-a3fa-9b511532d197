# 真实后端接口数据格式规范

## 1. 获取转盘页面数据

**接口：** `GET /api/wheel-page/{slug}`

**响应格式：**
```json
{
  "currentWheel": {
    "id": "what-to-eat",
    "name": "What to Eat Decision Wheel",
    "slug": "what-to-eat-decision-wheel",
    "prizes": [
      {
        "text": "Pizza",
        "color": "#ff6b6b",
        "image": "https://cdn.yourdomain.com/images/pizza.jpg" // 可选
      },
      {
        "text": "Chinese Food", 
        "color": "#4ecdc4"
      }
    ],
    "seo": {
      "title": "What to Eat Decision Wheel - Food Choice Spinner",
      "description": "Can't decide what to eat? Use our food decision wheel...",
      "keywords": ["what to eat", "food decision", "meal choice"]
    },
    "articles": [
      {
        "title": "Solving the Daily 'What to Eat' Dilemma",
        "content": "<p>HTML格式的文章内容...</p>"
      }
    ],
    "isDefault": false
  },
  "otherWheels": [
    {
      "id": "yes-no",
      "name": "Yes or No Decision Maker", 
      "slug": "yes-no-decision-maker",
      "description": "Make quick yes or no decisions..."
    }
  ],
  "success": true,
  "message": "Wheel data retrieved successfully"
}
```

## 2. 获取转盘列表

**接口：** `GET /api/wheel-list`

**响应格式：**
```json
{
  "wheels": [
    {
      "id": "yes-no",
      "name": "Yes or No Decision Maker",
      "slug": "yes-no-decision-maker", 
      "description": "Make quick yes or no decisions with our simple decision wheel..."
    },
    {
      "id": "what-to-eat",
      "name": "What to Eat Decision Wheel",
      "slug": "what-to-eat-decision-wheel",
      "description": "Can't decide what to eat? Use our food decision wheel..."
    }
  ],
  "total": 4,
  "success": true,
  "message": "Wheel list retrieved successfully"
}
```

## 3. 错误响应格式

**404 错误：**
```json
{
  "currentWheel": null,
  "otherWheels": [],
  "success": false,
  "message": "Wheel with slug 'non-existent' not found"
}
```

**500 错误：**
```json
{
  "wheels": [],
  "total": 0,
  "success": false,
  "message": "Internal server error"
}
```

## 4. 数据类型定义

### Prize 对象
```typescript
interface Prize {
  text: string;        // 选项文本
  color: string;       // 颜色值 (hex格式，如 "#ff6b6b")
  image?: string;      // 可选：图片URL
}
```

### WheelSEO 对象
```typescript
interface WheelSEO {
  title: string;       // SEO标题
  description: string; // SEO描述
  keywords?: string[]; // 可选：关键词数组
}
```

### WheelArticle 对象
```typescript
interface WheelArticle {
  title: string;       // 文章标题
  content: string;     // HTML格式的文章内容
}
```

### PresetWheel 对象
```typescript
interface PresetWheel {
  id: string;          // 转盘唯一ID
  name: string;        // 转盘显示名称
  slug: string;        // URL友好的slug
  prizes: Prize[];     // 转盘选项数组
  seo: WheelSEO;      // SEO信息
  articles: WheelArticle[]; // 文章内容数组
  isDefault?: boolean; // 可选：是否为默认转盘
}
```

### WheelListItem 对象
```typescript
interface WheelListItem {
  id: string;          // 转盘唯一ID
  name: string;        // 转盘显示名称
  slug: string;        // URL友好的slug
  description: string; // 转盘描述
}
```

## 5. 接口认证（如需要）

如果你的API需要认证，可以在请求头中添加：

```typescript
headers: {
  'Authorization': 'Bearer YOUR_API_TOKEN',
  'Content-Type': 'application/json'
}
```

## 6. 切换到真实API的步骤

1. **设置环境变量：**
   ```bash
   # .env.production
   NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com/v1
   NEXT_PUBLIC_SITE_URL=https://yourdomain.com
   ```

2. **确保后端API返回上述格式的数据**

3. **部署前端应用**

4. **测试API连接**

## 7. 本地测试真实API

如果想在本地测试真实API，修改 `.env.local`：

```bash
# .env.local
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com/v1
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## 8. API错误处理

前端已经实现了完整的错误处理：

- 404错误：显示"转盘未找到"页面
- 500错误：显示错误信息
- 网络错误：显示连接失败信息

确保你的后端API返回正确的HTTP状态码和错误信息。
