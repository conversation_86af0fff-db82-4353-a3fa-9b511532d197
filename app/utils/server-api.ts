/**
 * 服务端API调用工具
 * 用于在服务端组件中调用API获取数据
 */

import { config, endpoints } from '@/app/config/api';
import { WheelPageResponse, PresetWheel, WheelListItem } from '@/app/components/wheel/types';
import { getDefaultWheel, getWheelList, findWheelBySlug } from '@/app/data/presetWheels';

/**
 * 获取完整的API URL（服务端版本）
 */
function getServerApiUrl(endpoint: string): string {
  const baseUrl = config.apiBaseUrl;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  // 如果是相对路径（Mock API），需要转换为完整URL
  if (baseUrl.startsWith('/')) {
    // 在服务端，我们需要使用完整的localhost URL
    const serverUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    return `${serverUrl}${baseUrl}${cleanEndpoint}`;
  } else {
    // 如果是完整URL（真实API），直接使用
    return `${baseUrl}${cleanEndpoint}`;
  }
}

/**
 * 服务端API请求函数
 */
async function serverApiRequest<T>(endpoint: string): Promise<T> {
  const url = getServerApiUrl(endpoint);
  
  console.log(`🔗 Server API Call: GET ${url}`);
  
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
      },
      // 在构建时使用缓存，运行时根据环境决定
      cache: process.env.NODE_ENV === 'production' ? 'force-cache' : 'no-store',
    });
    
    if (!response.ok) {
      throw new Error(`Server API request failed: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  } catch (error) {
    console.error(`❌ Server API Error for ${url}:`, error);
    
    // 如果API调用失败，回退到Mock数据
    console.log('🔄 Falling back to mock data...');
    throw error;
  }
}

/**
 * 服务端获取转盘页面数据
 */
export async function getServerWheelPageData(slug: string): Promise<{ wheel: PresetWheel; otherWheels: WheelListItem[] }> {
  try {
    // 尝试调用真实API
    const data = await serverApiRequest<WheelPageResponse>(endpoints.wheelPage(slug));
    
    if (data.success) {
      return {
        wheel: data.currentWheel,
        otherWheels: data.otherWheels
      };
    } else {
      throw new Error(data.message || 'API returned unsuccessful response');
    }
  } catch (error) {
    console.warn('⚠️ API call failed, using mock data:', error);
    
    // 回退到Mock数据
    const wheel = findWheelBySlug(slug);
    if (!wheel) {
      throw new Error(`Wheel with slug "${slug}" not found`);
    }
    
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter(w => w.slug !== slug);
    
    return { wheel, otherWheels };
  }
}

/**
 * 服务端获取默认转盘数据（首页）
 * 使用空slug调用统一接口
 */
export async function getServerDefaultWheel(): Promise<{ defaultWheel: PresetWheel; otherWheels: WheelListItem[] }> {
  try {
    // 调用统一接口，slug为空表示获取默认转盘
    const data = await serverApiRequest<WheelPageResponse>(endpoints.wheelPage());

    if (data.success && data.currentWheel) {
      return {
        defaultWheel: data.currentWheel,
        otherWheels: data.otherWheels
      };
    } else {
      throw new Error(data.message || 'API returned unsuccessful response');
    }
  } catch (error) {
    console.warn('⚠️ API call failed, using mock data:', error);

    // 回退到Mock数据
    const defaultWheel = getDefaultWheel();
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter(w => w.slug !== defaultWheel.slug);

    return { defaultWheel, otherWheels };
  }
}

/**
 * 检查是否使用真实API
 */
export function isUsingRealAPI(): boolean {
  return config.apiBaseUrl !== '/api' && !config.apiBaseUrl.includes('localhost:3000');
}
