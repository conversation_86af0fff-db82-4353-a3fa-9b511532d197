import { NextResponse } from 'next/server';
import { getDefaultWheel } from '@/app/data/presetWheels';

export async function GET() {
  try {
    // 返回默认转盘的选项
    const defaultWheel = getDefaultWheel();
    return NextResponse.json(defaultWheel.prizes);
  } catch (error) {
    console.error('Error fetching default prizes:', error);
    // 备用数据
    const fallbackPrizes = [
      { text: "Yes", color: "#22c55e" },
      { text: "No", color: "#ef4444" },
    ];
    return NextResponse.json(fallbackPrizes);
  }
}