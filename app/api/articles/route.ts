import { NextResponse } from 'next/server';
import { getDefaultWheel } from '@/app/data/presetWheels';

export async function GET() {
  try {
    // 返回默认转盘的文章内容
    const defaultWheel = getDefaultWheel();
    return NextResponse.json(defaultWheel.articles);
  } catch (error) {
    console.error('Error fetching default articles:', error);
    // 备用文章内容
    const fallbackArticles = [
      {
        title: "What is DecisionsMaker?",
        content: `
          <p>
            DecisionsMaker is a free and intuitive online tool designed to help you make choices easily.
            Whether you're deciding where to eat, what movie to watch, or solving a group dilemma, our customizable spin wheel gives you a fun yet efficient way to make decisions.
          </p>
          <p>
            Just enter your options, spin the wheel, and let randomness make the tough calls for you.
          </p>
        `
      },
      {
        title: "How does the decision wheel work?",
        content: `
          <ol class="list-decimal list-inside space-y-3">
            <li>Type in the options you want to choose from</li>
            <li>Click the "Start" button to spin the wheel</li>
            <li>The wheel spins and gradually stops on a random option</li>
            <li>Your choice is displayed instantly</li>
          </ol>
          <p>
            The algorithm ensures each option has an equal chance, making it a fair random picker.
          </p>
        `
      }
    ];
    return NextResponse.json(fallbackArticles);
  }
}
