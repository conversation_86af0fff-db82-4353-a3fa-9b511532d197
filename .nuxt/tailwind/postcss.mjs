// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 6/26/2025, 9:55:50 AM
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.ts";
import cfg3 from "./../../tailwind.config.ts";
const config = [
{"content":{"files":["/Users/<USER>/Webs/makechoice/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Webs/makechoice/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Webs/makechoice/components/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Webs/makechoice/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Webs/makechoice/plugins/**/*.{js,ts,mjs}","/Users/<USER>/Webs/makechoice/composables/**/*.{js,ts,mjs}","/Users/<USER>/Webs/makechoice/utils/**/*.{js,ts,mjs}","/Users/<USER>/Webs/makechoice/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Webs/makechoice/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","/Users/<USER>/Webs/makechoice/app.config.{js,ts,mjs}"]}},
{},
cfg2,
cfg3
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;