<template>
  <div class="wheel-interactive">
    <LuckWheel
      :prizes="prizes"
      :wheel-id="wheelId"
      :title="title"
      :other-wheels="otherWheels"
      @prizes-change="handlePrizesChange"
    />
  </div>
</template>

<script setup lang="ts">
import type { Prize, WheelListItem } from '~/types'

interface Props {
  initialPrizes: Prize[]
  wheelId?: string
  title?: string
  otherWheels?: WheelListItem[]
}

const props = withDefaults(defineProps<Props>(), {
  initialPrizes: () => [],
  otherWheels: () => []
})

// 响应式数据
const prizes = ref<Prize[]>([...props.initialPrizes])

// 监听初始奖品变化
watch(() => props.initialPrizes, (newPrizes) => {
  prizes.value = [...newPrizes]
}, { immediate: true })

// 处理奖品变化
const handlePrizesChange = (newPrizes: Prize[]) => {
  prizes.value = [...newPrizes]
}

// 暴露给父组件的方法
defineExpose({
  getPrizes: () => prizes.value,
  setPrizes: (newPrizes: Prize[]) => {
    prizes.value = [...newPrizes]
  },
  resetPrizes: () => {
    prizes.value = [...props.initialPrizes]
  }
})
</script>

<style scoped>
.wheel-interactive {
  width: 100%;
  height: 100%;
}
</style>
