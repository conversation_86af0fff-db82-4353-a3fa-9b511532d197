/**
 * 处理首页请求（slug为空）
 * GET /api/wheel-page
 */

import type { WheelPageResponse } from '~/types'
import { getDefaultWheel, getWheelList } from '~/data/presetWheels'
import { handleServerError, createApiResponse, getCacheHeaders } from '~/utils/server-api'

export default defineEventHandler(async (event) => {
  try {
    // 设置缓存头
    setHeaders(event, getCacheHeaders(3600)) // 1小时缓存

    // 获取默认转盘（首页）
    const defaultWheel = getDefaultWheel()
    const allWheels = getWheelList()
    const otherWheels = allWheels.filter(w => w.slug !== defaultWheel.slug)

    const response: WheelPageResponse = {
      currentWheel: defaultWheel,
      otherWheels: otherWheels,
      success: true,
      message: 'Default wheel data retrieved successfully'
    }

    return response
  } catch (error) {
    console.error('Error in wheel-page API:', error)
    
    const errorResponse = handleServerError(error, 'wheel-page API')
    
    setResponseStatus(event, errorResponse.statusCode)
    
    const response: WheelPageResponse = {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: errorResponse.message
    }

    return response
  }
})
