{"name": "makechoice", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "check-config": "node scripts/check-config.js", "test-api": "node scripts/test-api.js", "set-env": "node scripts/set-environment.js"}, "dependencies": {"@nuxt/eslint": "^0.7.4", "@nuxtjs/color-mode": "^3.5.1", "@nuxtjs/tailwindcss": "^6.12.2", "@vercel/analytics": "^1.5.0", "nuxt": "^3.14.159", "tw-elements": "^2.0.0", "vue": "latest"}, "devDependencies": {"@types/node": "^20", "daisyui": "^5.0.0", "eslint": "^9", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}