{"name": "makechoice", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "check-config": "node scripts/check-config.js", "test-api": "node scripts/test-api.js", "set-env": "node scripts/set-environment.js"}, "dependencies": {"@tailwindcss/postcss": "^4.0.9", "@vercel/analytics": "^1.5.0", "next": "15.1.7", "react": "^19.0.0", "react-dom": "^19.0.0", "tw-elements": "^2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "daisyui": "^5.0.0", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}