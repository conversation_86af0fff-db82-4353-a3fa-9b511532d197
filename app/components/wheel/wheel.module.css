.container {
    position: relative;
    width: var(--wheel-size);
    margin: 50px auto;
  }
  
  .wheelWrapper {
    position: relative;
    width: var(--wheel-size);
    height: var(--wheel-size);
    border-radius: 50%;
    overflow: hidden;
  }
  
  .wheel {
    position: relative;
    width: 100%;
    height: 100%;
    display: block;
  }
  
  .prize {
    position: absolute;
    top: 0;
    left: 50%;
    width: 50%;
    height: 50%;
    transform-origin: 0% 100%;
  }
  
  .prizeContent {
    position: absolute;
    left: -100%;
    width: 200%;
    height: 200%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 20px;
  }
  
  .prizeImage {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
    object-fit: contain;
  }
  
  .prizeText {
    color: white;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  }
  
  .startButton {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: var(--start-button-size);
    height: var(--start-button-size);
    border-radius: 50%;
    background: #382f2f;
    color: white;
    border: none;
    cursor: pointer;
    z-index: 2;
    font-size: 20px;
    font-weight: bold;
    transition: all 0.3s ease;
  }
  
  .startButton:hover {
    background: #000;
  }
  
  .startButton:disabled {
    background: #382f2f;
    cursor: not-allowed;
  }
  
  .indicator {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 30px solid #000; /* 默认黑色 */
    z-index: 3;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)); /* 添加阴影增强可见性 */
  }

  /* 暗黑模式下的指示器样式 */
  :global(.dark) .indicator {
    border-top-color: #fff; /* 暗黑模式下使用白色 */
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.2)); /* 暗黑模式下使用白色阴影 */
  }