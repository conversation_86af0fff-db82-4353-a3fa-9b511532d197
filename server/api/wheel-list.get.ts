/**
 * 获取转盘列表
 * GET /api/wheel-list
 */

import { getWheelList } from '~/data/presetWheels'
import { handleServerError, getCacheHeaders } from '~/utils/server-api'

export default defineEventHandler(async (event) => {
  try {
    // 设置缓存头
    setHeaders(event, getCacheHeaders(3600)) // 1小时缓存

    // 获取转盘列表
    const wheelList = getWheelList()

    return wheelList
  } catch (error) {
    console.error('Error in wheel-list API:', error)
    
    const errorResponse = handleServerError(error, 'wheel-list API')
    
    setResponseStatus(event, errorResponse.statusCode)
    
    return {
      error: true,
      message: errorResponse.message,
      data: []
    }
  }
})
