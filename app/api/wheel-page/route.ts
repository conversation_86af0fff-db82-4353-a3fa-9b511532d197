import { NextResponse } from 'next/server';
import { getDefaultWheel, getWheelList } from '@/app/data/presetWheels';
import { WheelPageResponse } from '@/app/components/wheel/types';

/**
 * 处理首页请求（slug为空）
 * GET /api/wheel-page
 */
export async function GET() {
  try {
    // 获取默认转盘（首页）
    const defaultWheel = getDefaultWheel();
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter(w => w.slug !== defaultWheel.slug);

    const response: WheelPageResponse = {
      currentWheel: defaultWheel,
      otherWheels: otherWheels,
      success: true,
      message: 'Default wheel data retrieved successfully'
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error in wheel-page API:', error);
    
    const response = {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: 'Internal server error'
    };

    return NextResponse.json(response, { status: 500 });
  }
}
