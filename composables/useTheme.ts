/**
 * 主题相关的组合式函数
 */

import type { Theme } from '~/types'

/**
 * 主题管理（基于 @nuxtjs/color-mode）
 */
export function useTheme() {
  const colorMode = useColorMode()

  // 当前主题
  const theme = computed<Theme>(() => {
    return colorMode.preference as Theme
  })

  // 解析后的主题（实际应用的主题）
  const resolvedTheme = computed<'light' | 'dark'>(() => {
    return colorMode.value as 'light' | 'dark'
  })

  // 设置主题
  const setTheme = (newTheme: Theme) => {
    colorMode.preference = newTheme
  }

  // 切换主题
  const toggleTheme = () => {
    if (theme.value === 'light') {
      setTheme('dark')
    } else if (theme.value === 'dark') {
      setTheme('system')
    } else {
      setTheme('light')
    }
  }

  // 是否为深色主题
  const isDark = computed(() => resolvedTheme.value === 'dark')

  // 是否为浅色主题
  const isLight = computed(() => resolvedTheme.value === 'light')

  // 是否为系统主题
  const isSystem = computed(() => theme.value === 'system')

  // 获取主题图标
  const getThemeIcon = computed(() => {
    switch (theme.value) {
      case 'light':
        return '☀️'
      case 'dark':
        return '🌙'
      case 'system':
        return '💻'
      default:
        return '☀️'
    }
  })

  // 获取主题标签
  const getThemeLabel = computed(() => {
    switch (theme.value) {
      case 'light':
        return 'Light'
      case 'dark':
        return 'Dark'
      case 'system':
        return 'System'
      default:
        return 'Light'
    }
  })

  return {
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme,
    isDark,
    isLight,
    isSystem,
    getThemeIcon,
    getThemeLabel
  }
}

/**
 * 主题动画
 */
export function useThemeTransition() {
  const isTransitioning = ref(false)

  // 执行主题切换动画
  const executeThemeTransition = async (callback: () => void) => {
    if (isTransitioning.value) return

    isTransitioning.value = true

    // 添加过渡类
    if (process.client) {
      document.documentElement.classList.add('theme-transitioning')
    }

    // 执行主题切换
    callback()

    // 等待动画完成
    await new Promise(resolve => setTimeout(resolve, 300))

    // 移除过渡类
    if (process.client) {
      document.documentElement.classList.remove('theme-transitioning')
    }

    isTransitioning.value = false
  }

  return {
    isTransitioning: readonly(isTransitioning),
    executeThemeTransition
  }
}

/**
 * 主题持久化
 */
export function useThemePersistence() {
  const STORAGE_KEY = 'nuxt-color-mode'

  // 保存主题到本地存储
  const saveTheme = (theme: Theme) => {
    if (process.client) {
      localStorage.setItem(STORAGE_KEY, theme)
    }
  }

  // 从本地存储加载主题
  const loadTheme = (): Theme | null => {
    if (process.client) {
      return localStorage.getItem(STORAGE_KEY) as Theme
    }
    return null
  }

  // 清除保存的主题
  const clearTheme = () => {
    if (process.client) {
      localStorage.removeItem(STORAGE_KEY)
    }
  }

  return {
    saveTheme,
    loadTheme,
    clearTheme
  }
}

/**
 * 系统主题检测
 */
export function useSystemTheme() {
  const systemTheme = ref<'light' | 'dark'>('light')

  // 检测系统主题
  const detectSystemTheme = () => {
    if (process.client && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      systemTheme.value = mediaQuery.matches ? 'dark' : 'light'
      return systemTheme.value
    }
    return 'light'
  }

  // 监听系统主题变化
  const watchSystemTheme = (callback?: (theme: 'light' | 'dark') => void) => {
    if (process.client && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleChange = (e: MediaQueryListEvent) => {
        const newTheme = e.matches ? 'dark' : 'light'
        systemTheme.value = newTheme
        callback?.(newTheme)
      }

      mediaQuery.addEventListener('change', handleChange)

      // 返回清理函数
      return () => {
        mediaQuery.removeEventListener('change', handleChange)
      }
    }

    return () => {}
  }

  // 初始化
  onMounted(() => {
    detectSystemTheme()
    const cleanup = watchSystemTheme()

    onUnmounted(() => {
      cleanup()
    })
  })

  return {
    systemTheme: readonly(systemTheme),
    detectSystemTheme,
    watchSystemTheme
  }
}

/**
 * 主题相关的CSS变量
 */
export function useThemeVariables() {
  const { resolvedTheme } = useTheme()

  // 获取主题相关的CSS变量
  const getCSSVariables = computed(() => {
    const isDark = resolvedTheme.value === 'dark'

    return {
      '--color-primary': isDark ? '#3b82f6' : '#2563eb',
      '--color-secondary': isDark ? '#6b7280' : '#4b5563',
      '--color-background': isDark ? '#111827' : '#ffffff',
      '--color-surface': isDark ? '#1f2937' : '#f9fafb',
      '--color-text': isDark ? '#f9fafb' : '#111827',
      '--color-text-muted': isDark ? '#9ca3af' : '#6b7280',
      '--color-border': isDark ? '#374151' : '#e5e7eb',
      '--color-success': isDark ? '#10b981' : '#059669',
      '--color-warning': isDark ? '#f59e0b' : '#d97706',
      '--color-error': isDark ? '#ef4444' : '#dc2626',
      '--shadow-sm': isDark ? '0 1px 2px 0 rgba(0, 0, 0, 0.3)' : '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      '--shadow-md': isDark ? '0 4px 6px -1px rgba(0, 0, 0, 0.3)' : '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      '--shadow-lg': isDark ? '0 10px 15px -3px rgba(0, 0, 0, 0.3)' : '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
    }
  })

  // 应用CSS变量到文档根元素
  const applyCSSVariables = () => {
    if (process.client) {
      const root = document.documentElement
      const variables = getCSSVariables.value

      Object.entries(variables).forEach(([key, value]) => {
        root.style.setProperty(key, value)
      })
    }
  }

  // 监听主题变化并应用CSS变量
  watch(resolvedTheme, () => {
    applyCSSVariables()
  }, { immediate: true })

  return {
    getCSSVariables,
    applyCSSVariables
  }
}
