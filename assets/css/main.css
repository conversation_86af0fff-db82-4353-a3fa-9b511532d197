@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS 变量定义 */
:root {
  /* 字体 */
  --font-geist-sans: '<PERSON>eist Sans', system-ui, sans-serif;
  --font-geist-mono: 'Geist Mono', monospace;

  /* 颜色 - 浅色主题 */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-secondary: #6b7280;
  --color-background: #ffffff;
  --color-surface: #f9fafb;
  --color-text: #111827;
  --color-text-muted: #6b7280;
  --color-border: #e5e7eb;
  --color-success: #059669;
  --color-warning: #d97706;
  --color-error: #dc2626;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  /* 过渡 */
  --transition-colors: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 深色主题变量 */
.dark {
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-secondary: #9ca3af;
  --color-background: #111827;
  --color-surface: #1f2937;
  --color-text: #f9fafb;
  --color-text-muted: #9ca3af;
  --color-border: #374151;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  /* 深色主题阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
}

/* 全局样式 */
@layer base {
  html {
    font-family: var(--font-geist-sans);
  }

  body {
    @apply antialiased transition-colors;
    background-color: var(--color-background);
    color: var(--color-text);
    transition: var(--transition-colors);
  }

  /* 主题切换过渡 */
  .theme-transitioning * {
    transition: var(--transition-colors) !important;
  }
}

/* 组件样式 */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 font-medium py-2 px-4 rounded-lg transition-colors;
  }
}

/* 工具类 */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* 轮盘动画样式 */
.wheel-container {
  position: relative;
  display: inline-block;
}

.wheel-spin {
  transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 主题切换动画 */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-optimized {
    @apply text-sm;
  }
}

/* 深色模式特定样式 */
.dark {
  color-scheme: dark;
}

.light {
  color-scheme: light;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* 焦点样式 */
.focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

/* 卡片样式 */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6;
}

/* 输入框样式 */
.input {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

/* 按钮禁用状态 */
.btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 链接样式 */
.link {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline transition-colors;
}

/* 标题样式 */
.heading-1 {
  @apply text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100;
}

.heading-2 {
  @apply text-2xl md:text-3xl font-semibold text-gray-900 dark:text-gray-100;
}

.heading-3 {
  @apply text-xl md:text-2xl font-medium text-gray-900 dark:text-gray-100;
}

/* 文本样式 */
.text-muted {
  @apply text-gray-600 dark:text-gray-400;
}

.text-accent {
  @apply text-blue-600 dark:text-blue-400;
}

/* 容器样式 */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* 网格布局 */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* 阴影样式 */
.shadow-custom {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .shadow-custom {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* 过渡效果 */
.transition-custom {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
.hover-lift:hover {
  transform: translateY(-2px);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dark .gradient-bg {
  background: linear-gradient(135deg, #4c63d2 0%, #5a4fcf 100%);
}
