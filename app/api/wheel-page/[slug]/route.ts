import { NextRequest, NextResponse } from 'next/server';
import { findWheelBySlug, getWheelList } from '@/app/data/presetWheels';
import { WheelPageResponse } from '@/app/components/wheel/types';

/**
 * 统一的转盘页面接口
 * 返回当前转盘信息和其他转盘列表
 * 
 * @param slug - 转盘的URL slug
 * @returns WheelPageResponse - 包含当前转盘和其他转盘列表的完整响应
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    
    // 获取当前转盘信息
    const currentWheel = findWheelBySlug(slug);
    
    if (!currentWheel) {
      const response = {
        currentWheel: null,
        otherWheels: [],
        success: false,
        message: `Wheel with slug "${slug}" not found`
      };
      
      return NextResponse.json(response, { status: 404 });
    }
    
    // 获取所有转盘列表，并过滤掉当前转盘
    const allWheels = getWheelList();
    const otherWheels = allWheels.filter(wheel => wheel.slug !== slug);
    
    const response: WheelPageResponse = {
      currentWheel,
      otherWheels,
      success: true,
      message: 'Wheel data retrieved successfully'
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Error fetching wheel page data:', error);
    
    const response = {
      currentWheel: null,
      otherWheels: [],
      success: false,
      message: 'Internal server error'
    };
    
    return NextResponse.json(response, { status: 500 });
  }
}
