/* 轮盘容器 */
.container {
  position: relative;
  width: var(--wheel-size, 400px);
  height: var(--wheel-size, 400px);
  margin: 0 auto;
}

/* 指针 */
.indicator {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 30px solid #ef4444;
  z-index: 10;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* 轮盘包装器 */
.wheelWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 4px solid #ffffff;
}

.dark .wheelWrapper {
  border-color: #374151;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 轮盘画布 */
.wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.1s ease-out;
}

.wheel:hover {
  transform: scale(1.02);
}

/* 开始按钮 */
.startButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: var(--start-button-size, 80px);
  height: var(--start-button-size, 80px);
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  z-index: 20;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.startButton:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translate(-50%, -50%) scale(1.05);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.startButton:active {
  transform: translate(-50%, -50%) scale(0.95);
}

.startButton:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  cursor: not-allowed;
  transform: translate(-50%, -50%);
  box-shadow: 0 2px 8px rgba(156, 163, 175, 0.2);
}

/* 旋转动画 */
.wheel.spinning {
  transition: transform 8s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 结果显示 */
.result {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 20px;
  padding: 12px 24px;
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  white-space: nowrap;
  z-index: 30;
  animation: resultAppear 0.5s ease-out;
}

.dark .result {
  background: #1f2937;
  border-color: #374151;
  color: #f9fafb;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

@keyframes resultAppear {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 设置面板 */
.settingsPanel {
  position: fixed;
  top: 0;
  right: 0;
  width: 300px;
  height: 100vh;
  background: #ffffff;
  border-left: 1px solid #e5e7eb;
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);
  z-index: 50;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  overflow-y: auto;
}

.dark .settingsPanel {
  background: #1f2937;
  border-left-color: #374151;
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.3);
}

.settingsPanel.open {
  transform: translateX(0);
}

/* 设置按钮 */
.settingsButton {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: #ffffff;
  color: #374151;
  cursor: pointer;
  z-index: 60;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.dark .settingsButton {
  background: #374151;
  color: #f9fafb;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.settingsButton:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.dark .settingsButton:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* 全屏模式 */
.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dark .fullscreen {
  background: #111827;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    width: calc(100vw - 40px);
    height: calc(100vw - 40px);
    max-width: 350px;
    max-height: 350px;
  }

  .settingsPanel {
    width: 100%;
  }

  .startButton {
    font-size: 14px;
  }

  .result {
    font-size: 16px;
    padding: 10px 20px;
  }
}

@media (max-width: 480px) {
  .container {
    width: calc(100vw - 20px);
    height: calc(100vw - 20px);
    max-width: 300px;
    max-height: 300px;
  }

  .startButton {
    font-size: 12px;
  }

  .result {
    font-size: 14px;
    padding: 8px 16px;
  }
}

/* 加载状态 */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.loadingSpinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 100;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 无障碍支持 */
.wheel:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.startButton:focus {
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .wheelWrapper {
    border-width: 6px;
  }

  .startButton {
    border: 2px solid #ffffff;
  }

  .result {
    border-width: 3px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .wheel,
  .startButton,
  .settingsPanel,
  .result {
    transition: none;
  }

  .wheel.spinning {
    transition: transform 2s linear;
  }

  @keyframes resultAppear {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}
