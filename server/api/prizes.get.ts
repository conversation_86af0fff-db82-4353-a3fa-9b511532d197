/**
 * 获取默认奖品
 * GET /api/prizes
 */

import { getDefaultWheel } from '~/data/presetWheels'
import { handleServerError, getCacheHeaders } from '~/utils/server-api'

export default defineEventHandler(async (event) => {
  try {
    // 设置缓存头
    setHeaders(event, getCacheHeaders(3600)) // 1小时缓存

    // 返回默认转盘的选项
    const defaultWheel = getDefaultWheel()
    return defaultWheel.prizes
  } catch (error) {
    console.error('Error fetching default prizes:', error)
    
    const errorResponse = handleServerError(error, 'prizes API')
    
    setResponseStatus(event, errorResponse.statusCode)
    
    // 备用数据
    const fallbackPrizes = [
      { text: "Yes", color: "#22c55e" },
      { text: "No", color: "#ef4444" },
    ]
    
    return fallbackPrizes
  }
})
