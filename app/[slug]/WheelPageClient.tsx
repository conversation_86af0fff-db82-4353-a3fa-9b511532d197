"use client";
import { useState, useEffect } from 'react';
import { notFound } from 'next/navigation';
import LuckWheel from '@/app/components/wheel/wheel';
import { PresetWheel, Prize, WheelListItem } from '@/app/components/wheel/types';
import { getWheelPageData } from '@/app/utils/api';
import Link from 'next/link';
import Image from 'next/image';

interface WheelPageClientProps {
  slug: string;
}

export default function WheelPageClient({ slug }: WheelPageClientProps) {
  const [wheel, setWheel] = useState<PresetWheel | null>(null);
  const [prizes, setPrizes] = useState<Prize[]>([]);
  const [otherWheels, setOtherWheels] = useState<WheelListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWheelPageData = async () => {
      try {
        // 使用统一的 API 工具函数
        const data = await getWheelPageData(slug);

        if (!data.success) {
          if (data.message?.includes('not found')) {
            notFound();
          }
          throw new Error(data.message || 'Failed to load wheel data');
        }

        setWheel(data.currentWheel);
        setPrizes(data.currentWheel.prizes);
        setOtherWheels(data.otherWheels);

      } catch (error) {
        console.error('Error fetching wheel page data:', error);

        // 如果是 404 错误，跳转到 not found 页面
        if (error instanceof Error && error.message.includes('404')) {
          notFound();
        }

        setError('Failed to load wheel');
      } finally {
        setLoading(false);
      }
    };

    fetchWheelPageData();
  }, [slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !wheel) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600">{error || 'Wheel not found'}</p>
          <Link href="/" className="mt-4 inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <header className="bg-white shadow">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/" className="flex text-lg font-bold mr-20 items-center">
            <Image src="/logo.jpg" width={40} height={40} className="rounded mr-2" alt="Decisions Maker Online Logo" />
            <div>Decisions Maker Online</div>
          </Link>
        </div>
      </header>
      
      <div className="min-h-screen py-8 bg-white" id="wheelContainer">
        <div className="h-full flex flex-col items-center justify-center">
          <h1 className="text-3xl font-bold text-center mb-2">{wheel.name}</h1>
          <div className="flex flex-col items-center">
            <LuckWheel 
              prizes={prizes} 
              onPrizesChange={setPrizes}
            />
          </div>
        </div>
      </div>

      {/* 其他转盘导航 */}
      {otherWheels.length > 0 && (
        <section className="max-w-6xl mx-auto mt-16 px-4">
          <h2 className="text-2xl font-bold mb-6 text-center">Try Other Decision Wheels</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {otherWheels.map((otherWheel) => (
              <Link 
                key={otherWheel.id} 
                href={`/${otherWheel.slug}`}
                className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow"
              >
                <h3 className="font-semibold mb-2 text-blue-600">{otherWheel.name}</h3>
                <p className="text-gray-600 text-sm">{otherWheel.description}</p>
              </Link>
            ))}
          </div>
        </section>
      )}

      {/* 文章内容 */}
      <article className="max-w-4xl mx-auto mt-16 px-4">
        {wheel.articles.map((article, index) => (
          <section key={index} className="mb-12">
            <h2 className="text-2xl font-bold mb-6">{article.title}</h2>
            <div className="text-gray-700 leading-relaxed mb-4" 
                 dangerouslySetInnerHTML={{ __html: article.content }} />
          </section>
        ))}
      </article>
    </>
  );
}
