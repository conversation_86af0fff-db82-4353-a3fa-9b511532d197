<template>
  <div class="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center px-4">
    <div class="max-w-md w-full text-center">
      <!-- 错误图标 -->
      <div class="mb-8">
        <div class="mx-auto w-24 h-24 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
          <svg
            class="w-12 h-12 text-red-600 dark:text-red-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
      </div>

      <!-- 错误信息 -->
      <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
        {{ error.statusCode }}
      </h1>
      
      <h2 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
        {{ getErrorTitle() }}
      </h2>
      
      <p class="text-gray-600 dark:text-gray-400 mb-8">
        {{ getErrorMessage() }}
      </p>

      <!-- 操作按钮 -->
      <div class="space-y-4">
        <button
          @click="handleError"
          class="w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          {{ getActionText() }}
        </button>
        
        <NuxtLink
          to="/"
          class="block w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors font-medium"
        >
          Go to Homepage
        </NuxtLink>
      </div>

      <!-- 调试信息（仅开发环境） -->
      <div
        v-if="isDevelopment && error.stack"
        class="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg text-left"
      >
        <details>
          <summary class="cursor-pointer font-medium text-gray-900 dark:text-gray-100 mb-2">
            Debug Information
          </summary>
          <pre class="text-xs text-gray-600 dark:text-gray-400 overflow-auto">{{ error.stack }}</pre>
        </details>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ErrorProps {
  error: {
    statusCode: number
    statusMessage?: string
    message?: string
    stack?: string
    data?: any
  }
}

const props = defineProps<ErrorProps>()

// 检查是否为开发环境
const isDevelopment = process.env.NODE_ENV === 'development'

// 获取错误标题
const getErrorTitle = (): string => {
  switch (props.error.statusCode) {
    case 404:
      return 'Page Not Found'
    case 500:
      return 'Internal Server Error'
    case 403:
      return 'Access Forbidden'
    case 401:
      return 'Unauthorized'
    case 400:
      return 'Bad Request'
    default:
      return 'Something went wrong'
  }
}

// 获取错误消息
const getErrorMessage = (): string => {
  if (props.error.statusMessage) {
    return props.error.statusMessage
  }

  switch (props.error.statusCode) {
    case 404:
      return "The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."
    case 500:
      return "We're experiencing some technical difficulties. Please try again later."
    case 403:
      return "You don't have permission to access this resource."
    case 401:
      return "You need to be authenticated to access this page."
    case 400:
      return "The request was invalid. Please check your input and try again."
    default:
      return "An unexpected error occurred. Please try again later."
  }
}

// 获取操作按钮文本
const getActionText = (): string => {
  switch (props.error.statusCode) {
    case 404:
      return 'Go Back'
    case 500:
      return 'Retry'
    default:
      return 'Try Again'
  }
}

// 处理错误操作
const handleError = () => {
  switch (props.error.statusCode) {
    case 404:
      // 返回上一页或首页
      if (window.history.length > 1) {
        window.history.back()
      } else {
        navigateTo('/')
      }
      break
    case 500:
      // 重新加载页面
      window.location.reload()
      break
    default:
      // 清除错误并返回首页
      clearError({ redirect: '/' })
      break
  }
}

// SEO 配置
useSeoMeta({
  title: `${props.error.statusCode} - ${getErrorTitle()}`,
  description: getErrorMessage(),
  robots: 'noindex, nofollow'
})

// 页面配置
definePageMeta({
  layout: false
})

// 错误上报（生产环境）
onMounted(() => {
  if (!isDevelopment) {
    // 这里可以添加错误上报逻辑
    console.error('Page Error:', {
      statusCode: props.error.statusCode,
      message: props.error.message,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    })
  }
})
</script>

<style scoped>
/* 错误页面动画 */
.error-page-enter-active {
  transition: all 0.3s ease-out;
}

.error-page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

/* 按钮悬停效果 */
button:hover,
a:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark button:hover,
.dark a:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 调试信息样式 */
details summary::-webkit-details-marker {
  display: none;
}

details summary::before {
  content: '▶';
  margin-right: 0.5rem;
  transition: transform 0.2s ease;
}

details[open] summary::before {
  transform: rotate(90deg);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .max-w-md {
    max-width: 100%;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 1.25rem;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .bg-red-100 {
    background-color: #fecaca;
  }
  
  .dark .bg-red-900\/20 {
    background-color: rgba(127, 29, 29, 0.4);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .error-page-enter-active,
  button,
  a {
    transition: none;
  }
  
  button:hover,
  a:hover {
    transform: none;
  }
}
</style>
