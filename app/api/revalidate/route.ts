import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { path, tag, secret } = body;

    // 验证密钥（可选，用于安全性）
    const revalidateSecret = process.env.REVALIDATE_SECRET || 'your-secret-key';
    if (secret && secret !== revalidateSecret) {
      return NextResponse.json({ message: 'Invalid secret' }, { status: 401 });
    }

    // 重新验证特定路径
    if (path) {
      revalidatePath(path);
      console.log(`✅ Revalidated path: ${path}`);
      return NextResponse.json({ 
        revalidated: true, 
        path,
        message: `Path ${path} revalidated successfully` 
      });
    }

    // 重新验证特定标签
    if (tag) {
      revalidateTag(tag);
      console.log(`✅ Revalidated tag: ${tag}`);
      return NextResponse.json({ 
        revalidated: true, 
        tag,
        message: `Tag ${tag} revalidated successfully` 
      });
    }

    // 重新验证所有页面
    revalidatePath('/');
    revalidatePath('/[slug]', 'page');
    
    console.log('✅ Revalidated all pages');
    return NextResponse.json({ 
      revalidated: true, 
      message: 'All pages revalidated successfully' 
    });

  } catch (error) {
    console.error('❌ Revalidation error:', error);
    return NextResponse.json(
      { message: 'Error revalidating', error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// GET方法用于简单的重新验证
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path');
    const secret = searchParams.get('secret');

    // 验证密钥（可选）
    const revalidateSecret = process.env.REVALIDATE_SECRET || 'your-secret-key';
    if (secret && secret !== revalidateSecret) {
      return NextResponse.json({ message: 'Invalid secret' }, { status: 401 });
    }

    if (path) {
      revalidatePath(path);
      return NextResponse.json({ 
        revalidated: true, 
        path,
        message: `Path ${path} revalidated successfully` 
      });
    }

    // 默认重新验证所有页面
    revalidatePath('/');
    revalidatePath('/[slug]', 'page');
    
    return NextResponse.json({ 
      revalidated: true, 
      message: 'All pages revalidated successfully' 
    });

  } catch (error) {
    console.error('❌ Revalidation error:', error);
    return NextResponse.json(
      { message: 'Error revalidating', error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
