/**
 * 服务端API工具函数
 */

import type { WheelPageResponse, PresetWheel, WheelListItem } from '~/types'
import { findWheelBySlug, getDefaultWheel, getWheelList } from '~/data/presetWheels'

/**
 * 获取转盘页面数据（服务端）
 */
export async function getServerWheelPageData(slug: string): Promise<{
  wheel: PresetWheel;
  otherWheels: WheelListItem[];
}> {
  try {
    // 在服务端直接使用数据，不需要API调用
    const wheel = findWheelBySlug(slug)
    
    if (!wheel) {
      throw new Error(`Wheel with slug "${slug}" not found`)
    }
    
    const allWheels = getWheelList()
    const otherWheels = allWheels.filter(w => w.slug !== slug)
    
    return {
      wheel,
      otherWheels
    }
  } catch (error) {
    console.error('Error fetching server wheel page data:', error)
    throw error
  }
}

/**
 * 获取默认转盘数据（服务端）
 */
export async function getServerDefaultWheel(): Promise<{
  defaultWheel: PresetWheel;
  otherWheels: WheelListItem[];
}> {
  try {
    const defaultWheel = getDefaultWheel()
    const allWheels = getWheelList()
    const otherWheels = allWheels.filter(w => w.slug !== defaultWheel.slug)
    
    return {
      defaultWheel,
      otherWheels
    }
  } catch (error) {
    console.error('Error fetching server default wheel:', error)
    throw error
  }
}

/**
 * 获取所有转盘列表（服务端）
 */
export async function getServerWheelList(): Promise<WheelListItem[]> {
  try {
    return getWheelList()
  } catch (error) {
    console.error('Error fetching server wheel list:', error)
    throw error
  }
}

/**
 * 验证转盘slug是否存在（服务端）
 */
export function validateWheelSlug(slug: string): boolean {
  return !!findWheelBySlug(slug)
}

/**
 * 获取转盘的SEO数据（服务端）
 */
export function getWheelSeoData(slug?: string) {
  try {
    const wheel = slug ? findWheelBySlug(slug) : getDefaultWheel()
    
    if (!wheel) {
      return null
    }
    
    return {
      title: wheel.seo.title,
      description: wheel.seo.description,
      keywords: wheel.seo.keywords.join(', '),
      canonical: slug ? `https://decisionsmaker.online/${slug}` : 'https://decisionsmaker.online',
      ogImage: 'https://decisionsmaker.online/og-image.png'
    }
  } catch (error) {
    console.error('Error getting wheel SEO data:', error)
    return null
  }
}

/**
 * 生成站点地图数据（服务端）
 */
export function generateSitemapData() {
  try {
    const wheels = getWheelList()
    const baseUrl = 'https://decisionsmaker.online'
    
    const urls = [
      {
        loc: baseUrl,
        lastmod: new Date().toISOString(),
        changefreq: 'daily',
        priority: 1.0
      },
      ...wheels.map(wheel => ({
        loc: `${baseUrl}/${wheel.slug}`,
        lastmod: new Date().toISOString(),
        changefreq: 'weekly',
        priority: 0.8
      }))
    ]
    
    return urls
  } catch (error) {
    console.error('Error generating sitemap data:', error)
    return []
  }
}

/**
 * 生成robots.txt内容（服务端）
 */
export function generateRobotsContent(): string {
  const baseUrl = 'https://decisionsmaker.online'
  
  return `User-agent: *
Allow: /

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml

# Crawl-delay
Crawl-delay: 1

# Disallow admin areas
Disallow: /admin/
Disallow: /api/

# Allow specific API endpoints for SEO
Allow: /api/wheel-page/
Allow: /api/wheel-list/`
}

/**
 * 处理API错误（服务端）
 */
export function handleServerError(error: any, context: string) {
  console.error(`Server error in ${context}:`, error)
  
  // 根据错误类型返回适当的HTTP状态码和消息
  if (error.message?.includes('not found')) {
    return {
      statusCode: 404,
      message: 'Resource not found'
    }
  } else if (error.message?.includes('validation')) {
    return {
      statusCode: 400,
      message: 'Invalid request'
    }
  } else {
    return {
      statusCode: 500,
      message: 'Internal server error'
    }
  }
}

/**
 * 创建API响应（服务端）
 */
export function createApiResponse<T>(
  data: T,
  success: boolean = true,
  message?: string,
  statusCode: number = 200
) {
  return {
    data,
    success,
    message,
    statusCode,
    timestamp: new Date().toISOString()
  }
}

/**
 * 验证请求参数（服务端）
 */
export function validateRequestParams(
  params: Record<string, any>,
  required: string[]
): { valid: boolean; missing?: string[] } {
  const missing = required.filter(key => !params[key])
  
  return {
    valid: missing.length === 0,
    missing: missing.length > 0 ? missing : undefined
  }
}

/**
 * 缓存控制头（服务端）
 */
export function getCacheHeaders(maxAge: number = 3600) {
  return {
    'Cache-Control': `public, max-age=${maxAge}, s-maxage=${maxAge}`,
    'Vary': 'Accept-Encoding'
  }
}

/**
 * CORS头（服务端）
 */
export function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  }
}
