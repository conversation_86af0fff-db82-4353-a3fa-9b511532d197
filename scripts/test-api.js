#!/usr/bin/env node

/**
 * API测试脚本
 * 用于测试API配置是否正确工作
 */

const fs = require('fs');
const path = require('path');

// 读取环境变量
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        const value = valueParts.join('=');
        if (key && value) {
          process.env[key] = value;
        }
      }
    });
  }
}

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testAPI() {
  loadEnvFile();
  
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
  
  log('\n🔍 API配置测试', 'blue');
  log('================', 'blue');
  log(`API Base URL: ${apiBaseUrl}`, 'yellow');
  log(`Site URL: ${siteUrl}`, 'yellow');
  
  // 构建测试URL
  let testUrl;
  if (apiBaseUrl.startsWith('/')) {
    testUrl = `${siteUrl}${apiBaseUrl}/wheel-list`;
  } else {
    testUrl = `${apiBaseUrl}/wheel-list`;
  }
  
  log(`\n🔗 测试URL: ${testUrl}`, 'blue');
  
  try {
    log('\n⏳ 发送请求...', 'yellow');
    
    const response = await fetch(testUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    log(`📊 响应状态: ${response.status} ${response.statusText}`, 
        response.ok ? 'green' : 'red');
    
    if (response.ok) {
      const data = await response.json();
      log('✅ API调用成功!', 'green');
      log(`📝 返回数据:`, 'blue');
      console.log(JSON.stringify(data, null, 2));
      
      // 检查数据格式
      if (data.success && Array.isArray(data.wheels)) {
        log(`✅ 数据格式正确，包含 ${data.wheels.length} 个转盘`, 'green');
      } else if (Array.isArray(data)) {
        log('⚠️  返回数组格式（可能是Mock API）', 'yellow');
      } else {
        log('⚠️  数据格式不符合预期', 'yellow');
      }
    } else {
      log('❌ API调用失败', 'red');
      const errorText = await response.text();
      log(`错误信息: ${errorText}`, 'red');
    }
    
  } catch (error) {
    log('❌ 网络错误', 'red');
    log(`错误详情: ${error.message}`, 'red');
    
    if (apiBaseUrl.startsWith('/')) {
      log('\n💡 提示: 你正在使用相对路径API，请确保Next.js开发服务器正在运行', 'yellow');
    } else {
      log('\n💡 提示: 请检查你的API服务器是否正在运行并且可以访问', 'yellow');
    }
  }
}

// 运行测试
testAPI().catch(console.error);
