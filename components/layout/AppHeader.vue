<template>
  <header class="bg-white dark:bg-gray-800 shadow border-b border-gray-200 dark:border-gray-700">
    <div class="container mx-auto px-4 py-4 flex justify-between items-center">
      <!-- Logo和标题 -->
      <NuxtLink 
        to="/" 
        class="flex text-lg font-bold mr-20 items-center hover:opacity-80 transition-opacity"
      >
        <NuxtImg 
          src="/logo.jpg" 
          width="40" 
          height="40" 
          class="rounded mr-2" 
          alt="Decisions Maker Online Logo"
          loading="eager"
        />
        <div class="text-gray-900 dark:text-gray-100">
          Decisions Maker Online
        </div>
      </NuxtLink>

      <!-- 导航菜单（桌面端） -->
      <nav class="hidden md:flex items-center space-x-6">
        <NuxtLink
          v-for="item in navigationItems"
          :key="item.href"
          :to="item.href"
          class="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
          :class="{ 'text-blue-600 dark:text-blue-400': isActiveRoute(item.href) }"
        >
          {{ item.name }}
        </NuxtLink>
      </nav>

      <!-- 右侧操作区 -->
      <div class="flex items-center space-x-4">
        <!-- 主题切换按钮 -->
        <ThemeToggle />

        <!-- 移动端菜单按钮 -->
        <button
          @click="toggleMobileMenu"
          class="md:hidden p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          aria-label="Toggle mobile menu"
        >
          <svg
            class="w-5 h-5 text-gray-600 dark:text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              v-if="!showMobileMenu"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
            <path
              v-else
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div
      v-if="showMobileMenu"
      class="md:hidden border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
    >
      <nav class="px-4 py-2 space-y-1">
        <NuxtLink
          v-for="item in navigationItems"
          :key="item.href"
          :to="item.href"
          @click="closeMobileMenu"
          class="block px-3 py-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          :class="{ 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20': isActiveRoute(item.href) }"
        >
          {{ item.name }}
        </NuxtLink>
      </nav>
    </div>
  </header>
</template>

<script setup lang="ts">
import type { NavigationItem } from '~/types'

// 导航项配置
const navigationItems: NavigationItem[] = [
  { name: 'Home', href: '/' },
  { name: 'Yes/No', href: '/yes-no-decision-maker' },
  { name: 'Food', href: '/what-to-eat-decision-wheel' },
  { name: 'Movies', href: '/movie-night-picker' },
  { name: 'Activities', href: '/weekend-activity-planner' },
]

// 响应式数据
const showMobileMenu = ref(false)

// 路由相关
const route = useRoute()

// 检查是否为活跃路由
const isActiveRoute = (href: string): boolean => {
  if (href === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(href)
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 监听路由变化，关闭移动端菜单
watch(() => route.path, () => {
  closeMobileMenu()
})

// 点击外部关闭移动端菜单
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement
    const header = target.closest('header')
    
    if (!header && showMobileMenu.value) {
      closeMobileMenu()
    }
  }

  document.addEventListener('click', handleClickOutside)

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})

// SEO和可访问性
useHead({
  link: [
    {
      rel: 'preload',
      href: '/logo.jpg',
      as: 'image'
    }
  ]
})
</script>

<style scoped>
/* 导航链接动画 */
nav a {
  position: relative;
  transition: all 0.2s ease-in-out;
}

nav a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: currentColor;
  transition: width 0.2s ease-in-out;
}

nav a:hover::after,
nav a.router-link-active::after {
  width: 100%;
}

/* 移动端菜单动画 */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: all 0.3s ease-in-out;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Logo悬停效果 */
.logo-link:hover img {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .border-gray-200 {
    border-color: #000000;
  }
  
  .dark .border-gray-700 {
    border-color: #ffffff;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  nav a,
  nav a::after,
  .mobile-menu-enter-active,
  .mobile-menu-leave-active {
    transition: none;
  }
}
</style>
