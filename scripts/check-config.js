#!/usr/bin/env node

/**
 * 配置检查脚本
 * 用于验证环境变量和API配置是否正确
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkEnvironmentFiles() {
  log('\n🔍 检查环境配置文件...', 'blue');
  
  const envFiles = ['.env.local', '.env.production', '.env.example'];
  const results = [];
  
  envFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    const exists = fs.existsSync(filePath);
    
    if (exists) {
      log(`✅ ${file} 存在`, 'green');
      
      // 读取文件内容检查配置
      const content = fs.readFileSync(filePath, 'utf8');
      const hasApiUrl = content.includes('NEXT_PUBLIC_API_BASE_URL');
      const hasSiteUrl = content.includes('NEXT_PUBLIC_SITE_URL');
      
      if (hasApiUrl) {
        log(`   ✅ 包含 API_BASE_URL 配置`, 'green');
      } else {
        log(`   ⚠️  缺少 API_BASE_URL 配置`, 'yellow');
      }
      
      if (hasSiteUrl) {
        log(`   ✅ 包含 SITE_URL 配置`, 'green');
      } else {
        log(`   ⚠️  缺少 SITE_URL 配置`, 'yellow');
      }
      
      results.push({ file, exists: true, hasApiUrl, hasSiteUrl });
    } else {
      log(`❌ ${file} 不存在`, 'red');
      results.push({ file, exists: false });
    }
  });
  
  return results;
}

function checkApiConfiguration() {
  log('\n🔍 检查API配置...', 'blue');
  
  const configPath = path.join(process.cwd(), 'app/config/api.ts');
  const utilsPath = path.join(process.cwd(), 'app/utils/api.ts');
  
  if (fs.existsSync(configPath)) {
    log('✅ API配置文件存在', 'green');
  } else {
    log('❌ API配置文件不存在', 'red');
  }
  
  if (fs.existsSync(utilsPath)) {
    log('✅ API工具文件存在', 'green');
  } else {
    log('❌ API工具文件不存在', 'red');
  }
}

function checkDocumentation() {
  log('\n🔍 检查文档...', 'blue');
  
  const docs = [
    'docs/api-documentation.md',
    'docs/real-api-format.md'
  ];
  
  docs.forEach(doc => {
    const docPath = path.join(process.cwd(), doc);
    if (fs.existsSync(docPath)) {
      log(`✅ ${doc} 存在`, 'green');
    } else {
      log(`❌ ${doc} 不存在`, 'red');
    }
  });
}

function generateConfigTemplate() {
  log('\n📝 生成配置模板...', 'blue');
  
  const template = `# 环境配置模板
# 复制此文件为 .env.local (开发环境) 或 .env.production (生产环境)

# API配置
# 开发环境使用本地Mock API
NEXT_PUBLIC_API_BASE_URL=/api

# 生产环境使用真实API (请替换为你的实际域名)
# NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com/v1

# 网站URL配置
# 开发环境
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# 生产环境 (请替换为你的实际域名)
# NEXT_PUBLIC_SITE_URL=https://yourdomain.com

# 其他配置
# NEXT_PUBLIC_API_TOKEN=your_api_token_here
`;

  const templatePath = path.join(process.cwd(), '.env.template');
  fs.writeFileSync(templatePath, template);
  log(`✅ 配置模板已生成: .env.template`, 'green');
}

function showUsageInstructions() {
  log('\n📖 使用说明:', 'blue');
  log('1. 开发环境配置:', 'yellow');
  log('   - 复制 .env.template 为 .env.local');
  log('   - 保持 NEXT_PUBLIC_API_BASE_URL=/api 使用Mock API');
  
  log('\n2. 生产环境配置:', 'yellow');
  log('   - 复制 .env.template 为 .env.production');
  log('   - 设置 NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com/v1');
  log('   - 设置 NEXT_PUBLIC_SITE_URL=https://yourdomain.com');
  
  log('\n3. 后端API要求:', 'yellow');
  log('   - 查看 docs/real-api-format.md 了解数据格式');
  log('   - 确保API返回正确的JSON格式');
  log('   - 实现错误处理和状态码');
}

function main() {
  log('🚀 配置检查工具', 'blue');
  log('================', 'blue');
  
  checkEnvironmentFiles();
  checkApiConfiguration();
  checkDocumentation();
  generateConfigTemplate();
  showUsageInstructions();
  
  log('\n✨ 检查完成!', 'green');
}

// 运行检查
main();
