/**
 * API 配置管理
 * 统一管理所有API相关的配置
 */

import { getEnvironmentConfig, getApiEndpoints, requestConfig } from './environment'
import type { APIEndpoints, RequestConfig } from '~/types'

// 环境配置
export const config = getEnvironmentConfig()

// API端点配置
export const endpoints: APIEndpoints = {
  // 获取转盘页面数据（统一接口）
  wheelPage: (slug?: string) => slug ? `/wheel-page/${slug}` : '/wheel-page',
}

// 请求配置
export const apiRequestConfig: RequestConfig = requestConfig

/**
 * 创建API请求函数
 */
export function createApiRequest() {
  const { apiBaseUrl } = config
  
  return {
    /**
     * GET请求
     */
    async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
      const url = endpoint.startsWith('http') ? endpoint : `${apiBaseUrl}${endpoint}`
      
      const response = await $fetch<T>(url, {
        method: 'GET',
        headers: {
          ...apiRequestConfig.defaultHeaders,
          ...options?.headers
        },
        ...options
      })
      
      return response
    },
    
    /**
     * POST请求
     */
    async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
      const url = endpoint.startsWith('http') ? endpoint : `${apiBaseUrl}${endpoint}`
      
      const response = await $fetch<T>(url, {
        method: 'POST',
        headers: {
          ...apiRequestConfig.defaultHeaders,
          ...options?.headers
        },
        body: data,
        ...options
      })
      
      return response
    },
    
    /**
     * PUT请求
     */
    async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
      const url = endpoint.startsWith('http') ? endpoint : `${apiBaseUrl}${endpoint}`
      
      const response = await $fetch<T>(url, {
        method: 'PUT',
        headers: {
          ...apiRequestConfig.defaultHeaders,
          ...options?.headers
        },
        body: data,
        ...options
      })
      
      return response
    },
    
    /**
     * DELETE请求
     */
    async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
      const url = endpoint.startsWith('http') ? endpoint : `${apiBaseUrl}${endpoint}`
      
      const response = await $fetch<T>(url, {
        method: 'DELETE',
        headers: {
          ...apiRequestConfig.defaultHeaders,
          ...options?.headers
        },
        ...options
      })
      
      return response
    }
  }
}

/**
 * 默认API实例
 */
export const api = createApiRequest()

/**
 * API错误处理
 */
export function handleApiError(error: any) {
  console.error('API Error:', error)
  
  // 根据错误类型返回用户友好的错误信息
  if (error.status === 404) {
    return 'Resource not found'
  } else if (error.status === 500) {
    return 'Internal server error'
  } else if (error.status >= 400 && error.status < 500) {
    return 'Client error'
  } else if (error.status >= 500) {
    return 'Server error'
  } else {
    return 'Network error'
  }
}

/**
 * 重试机制
 */
export async function retryRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = apiRequestConfig.retryCount
): Promise<T> {
  let lastError: any
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error
      
      // 如果是最后一次重试，抛出错误
      if (i === maxRetries) {
        throw error
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
  
  throw lastError
}
