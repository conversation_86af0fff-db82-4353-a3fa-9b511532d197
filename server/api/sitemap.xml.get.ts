/**
 * 生成站点地图
 * GET /api/sitemap.xml
 */

import { generateSitemapData } from '~/utils/server-api'

export default defineEventHandler(async (event) => {
  try {
    // 设置响应头
    setHeader(event, 'Content-Type', 'application/xml')
    setHeader(event, 'Cache-Control', 'public, max-age=3600, s-maxage=3600')

    // 生成站点地图数据
    const urls = generateSitemapData()

    // 构建 XML
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.map(url => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('\n')}
</urlset>`

    return sitemap
  } catch (error) {
    console.error('Error generating sitemap:', error)
    
    setResponseStatus(event, 500)
    setHeader(event, 'Content-Type', 'text/plain')
    
    return 'Error generating sitemap'
  }
})
