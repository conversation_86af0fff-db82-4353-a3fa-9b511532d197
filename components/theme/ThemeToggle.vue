<template>
  <div class="relative">
    <button
      @click="toggleTheme"
      class="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900"
      :title="`Switch to ${getNextThemeLabel} theme`"
      :aria-label="`Switch to ${getNextThemeLabel} theme`"
    >
      <span class="text-lg" role="img" :aria-label="getThemeLabel">
        {{ getThemeIcon }}
      </span>
    </button>
    
    <!-- 主题选择下拉菜单（可选） -->
    <div
      v-if="showDropdown"
      class="absolute right-0 mt-2 w-32 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
    >
      <button
        v-for="themeOption in themeOptions"
        :key="themeOption.value"
        @click="setTheme(themeOption.value)"
        class="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg"
        :class="{
          'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400': theme === themeOption.value
        }"
      >
        <span class="mr-2">{{ themeOption.icon }}</span>
        {{ themeOption.label }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Theme } from '~/types'

interface Props {
  showDropdown?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showDropdown: false
})

// 使用主题组合式函数
const { 
  theme, 
  setTheme, 
  toggleTheme, 
  getThemeIcon, 
  getThemeLabel 
} = useTheme()

// 主题选项
const themeOptions = [
  { value: 'light' as Theme, label: 'Light', icon: '☀️' },
  { value: 'dark' as Theme, label: 'Dark', icon: '🌙' },
  { value: 'system' as Theme, label: 'System', icon: '💻' }
]

// 获取下一个主题的标签
const getNextThemeLabel = computed(() => {
  switch (theme.value) {
    case 'light':
      return 'dark'
    case 'dark':
      return 'system'
    case 'system':
      return 'light'
    default:
      return 'dark'
  }
})

// 点击外部关闭下拉菜单
const dropdown = ref<HTMLElement>()

onMounted(() => {
  if (props.showDropdown) {
    const handleClickOutside = (event: Event) => {
      if (dropdown.value && !dropdown.value.contains(event.target as Node)) {
        // 关闭下拉菜单的逻辑
      }
    }

    document.addEventListener('click', handleClickOutside)

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
  }
})
</script>

<style scoped>
/* 主题切换动画 */
button {
  transition: all 0.2s ease-in-out;
}

button:hover {
  transform: scale(1.05);
}

button:active {
  transform: scale(0.95);
}

/* 下拉菜单动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease-in-out;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
