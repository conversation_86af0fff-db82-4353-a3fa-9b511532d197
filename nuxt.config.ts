// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  
  // CSS框架
  css: ['~/assets/css/main.css'],
  
  // 模块
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/color-mode',
    '@nuxt/eslint'
  ],
  
  // 颜色模式配置
  colorMode: {
    preference: 'system', // 默认主题
    fallback: 'light', // 回退主题
    hid: 'nuxt-color-mode-script',
    globalName: '__NUXT_COLOR_MODE__',
    componentName: 'ColorScheme',
    classPrefix: '',
    classSuffix: '',
    storageKey: 'nuxt-color-mode'
  },
  
  // Tailwind CSS配置
  tailwindcss: {
    cssPath: '~/assets/css/main.css',
    configPath: 'tailwind.config.ts',
    exposeConfig: false,
    viewer: true,
  },
  
  // 应用配置
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'DecisionsMaker Online - Smart Decision Making Wheel',
      meta: [
        { name: 'description', content: 'Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly. Perfect for personal and business decisions.' },
        { name: 'keywords', content: 'decision making, decision wheel, random choice generator, decisionsmaker online, online decision tool, decision making tool' },
        { name: 'author', content: 'DecisionsMaker Online' },
        { name: 'robots', content: 'index, follow' },
        // Open Graph
        { property: 'og:title', content: 'DecisionsMaker Online - Smart Decision Making Wheel' },
        { property: 'og:description', content: 'Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly.' },
        { property: 'og:url', content: 'https://decisionsmaker.online' },
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: 'DecisionsMaker Online' },
        { property: 'og:image', content: 'https://decisionsmaker.online/og-image.png' },
        { property: 'og:image:width', content: '1200' },
        { property: 'og:image:height', content: '630' },
        { property: 'og:image:alt', content: 'DecisionsMaker Online Logo' },
        // Twitter
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: 'DecisionsMaker Online - Smart Decision Making Wheel' },
        { name: 'twitter:description', content: 'Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly.' },
        { name: 'twitter:site', content: '@decisionsmaker' },
        { name: 'twitter:creator', content: '@decisionsmaker' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: 'https://decisionsmaker.online' }
      ],
      script: [
        // Google Analytics
        {
          src: 'https://www.googletagmanager.com/gtag/js?id=G-36JS659442',
          async: true
        },
        {
          innerHTML: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-36JS659442');
          `,
          type: 'text/javascript'
        },
        // Baidu Analytics
        {
          innerHTML: `
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?b209671ea8fa33b06ef52e4025885a97";
              var s = document.getElementsByTagName("script")[0]; 
              s.parentNode.insertBefore(hm, s);
            })();
          `,
          type: 'text/javascript'
        }
      ]
    }
  },
  
  // 图片优化
  image: {
    domains: ['www.yunwuye.online']
  },
  
  // 服务端渲染配置
  ssr: true,
  
  // 实验性功能
  experimental: {
    payloadExtraction: false
  },
  
  // Nitro配置（用于ISR）
  nitro: {
    prerender: {
      routes: ['/sitemap.xml', '/robots.txt']
    }
  },
  
  // 路由配置
  router: {
    options: {
      strict: false
    }
  },
  
  // TypeScript配置
  typescript: {
    strict: true,
    typeCheck: true
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅服务端可用）
    apiSecret: process.env.API_SECRET || '',

    // 公共配置（客户端和服务端都可用）
    public: {
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:3000/api',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'https://decisionsmaker.online',
      gtag: process.env.NUXT_PUBLIC_GTAG || 'G-36JS659442',
      baiduAnalytics: process.env.NUXT_PUBLIC_BAIDU_ANALYTICS || 'b209671ea8fa33b06ef52e4025885a97'
    }
  }
})
