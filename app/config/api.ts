/**
 * API 配置管理
 * 统一管理所有API相关的配置
 */

import { getEnvironmentConfig } from './environment';

// 环境配置
export const config = getEnvironmentConfig();

// API端点配置
export const endpoints = {
  // 获取转盘页面数据（统一接口）
  wheelPage: (slug?: string) => slug ? `/wheel-page/${slug}` : '/wheel-page/',
};

// 请求配置
export const requestConfig = {
  // 默认请求头
  defaultHeaders: {
    'Content-Type': 'application/json',
    // 如果需要认证，可以在这里添加
    // 'Authorization': `Bearer ${process.env.NEXT_PUBLIC_API_TOKEN}`,
  },
  
  // 请求超时时间（毫秒）
  timeout: 10000,
  
  // 重试次数
  retryCount: 3,
};

// 获取完整的API URL
export function getApiUrl(endpoint: string): string {
  const baseUrl = config.apiBaseUrl;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  if (baseUrl.startsWith('http')) {
    return `${baseUrl}${cleanEndpoint}`;
  } else {
    return `${cleanEndpoint}`;
  }
}

// 获取完整的网站URL
export function getSiteUrl(path: string = ''): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${config.siteUrl}${cleanPath}`;
}

// 日志配置
export const logConfig = {
  // 是否启用API请求日志
  enableApiLogs: config.isDevelopment,
  
  // 是否启用错误日志
  enableErrorLogs: true,
};

// 开发环境辅助函数
export function logApiCall(endpoint: string, method: string = 'GET') {
  if (logConfig.enableApiLogs) {
    console.log(`🔗 API Call: ${method} ${getApiUrl(endpoint)}`);
    if (config.useMockApi) {
      console.log('📝 Using Mock API');
    } else {
      console.log('🌐 Using Real API');
    }
  }
}

// 环境检查函数
export function validateEnvironment() {
  const warnings: string[] = [];
  
  if (!config.siteUrl || config.siteUrl.includes('yourdomain.com')) {
    warnings.push('⚠️  NEXT_PUBLIC_SITE_URL not configured properly');
  }
  
  if (config.useMockApi && !config.isDevelopment) {
    warnings.push('⚠️  Using Mock API in production environment');
  }
  
  if (warnings.length > 0 && logConfig.enableErrorLogs) {
    console.warn('Environment Configuration Warnings:');
    warnings.forEach(warning => console.warn(warning));
  }
  
  return warnings.length === 0;
}

// 导出默认配置
export default config;
