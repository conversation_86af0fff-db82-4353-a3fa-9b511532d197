import Link from 'next/link';
import Image from 'next/image';
import { PresetWheel, WheelListItem } from '@/app/components/wheel/types';
import WheelInteractive from './[slug]/WheelInteractive';
import ThemeToggle from './components/theme/ThemeToggle';
import RichTextContent from './components/ui/RichTextContent';

interface HomePageServerProps {
  defaultWheel: PresetWheel;
  otherWheels: WheelListItem[];
}

// 服务端渲染的主页组件
export default function HomePageServer({ defaultWheel, otherWheels }: HomePageServerProps) {
  return (
    <>
      <header className="bg-white dark:bg-gray-800 shadow border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex text-lg font-bold mr-20 items-center">
            <Image src="/logo.jpg" width={40} height={40} className="rounded mr-2" alt="Decisions Maker Online Logo" />
            <div className="text-gray-900 dark:text-gray-100">Decisions Maker Online</div>
          </div>
          <div className="flex items-center space-x-4">
            <ThemeToggle />
          </div>
        </div>
      </header>
      
      <div className="min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden" id="wheelContainer">
        <div className="h-full flex flex-col items-center max-w-full">
          <h1 className="text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0">{defaultWheel.name}</h1>
          <div className="flex flex-col items-center flex-grow w-full">
            {/* 交互式转盘组件 */}
            <WheelInteractive
              initialPrizes={defaultWheel.prizes}
              wheelId={defaultWheel.slug}
              title={defaultWheel.name}
            />
          </div>
        </div>
      </div>

      {/* 其他转盘导航 - 服务端渲染 */}
      {otherWheels.length > 0 && (
        <section className="max-w-6xl mx-auto mt-16 px-4">
          <h2 className="text-2xl font-bold mb-6 text-center text-gray-900 dark:text-gray-100">Try Other Decision Wheels</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {otherWheels.map((wheel) => (
              <Link
                key={wheel.id}
                href={`/${wheel.slug}`}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
              >
                <h3 className="font-semibold mb-2 text-blue-600 dark:text-blue-400">{wheel.name}</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">{wheel.description}</p>
              </Link>
            ))}
          </div>
        </section>
      )}

      {/* 文章内容 - 服务端渲染，SEO友好 */}
      <article className="max-w-4xl mx-auto mt-16 px-4">
        {defaultWheel.articles.map((article, index) => (
          <section key={index} className="mb-12">
            <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">{article.title}</h2>
            <RichTextContent
              content={article.content}
              className="leading-relaxed mb-4"
            />
          </section>
        ))}
      </article>

      {/* 结构化数据 - SEO优化 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "Decisions Maker Online",
            "description": defaultWheel.seo.description,
            "url": process.env.NEXT_PUBLIC_SITE_URL || "https://yourdomain.com",
            "applicationCategory": "UtilityApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "author": {
              "@type": "Organization",
              "name": "Decisions Maker Online"
            },
            "potentialAction": {
              "@type": "UseAction",
              "target": process.env.NEXT_PUBLIC_SITE_URL || "https://yourdomain.com"
            }
          })
        }}
      />
    </>
  );
}
