import { useEffect, useRef, useState, useCallback, ChangeEvent } from 'react';
import { LuckWheelProps, Prize } from './types';
import styles from './wheel.module.css';
import SelectedModal from '@/app/components/selected_modal/selected_modal';
import Image from 'next/image';

export default function LuckWheel(props: LuckWheelProps) {
  const startButtonSize = 80
  const { prizes, onPrizesChange, wheelId, title } = props;

  // 动态生成localStorage的key，为不同页面使用不同的key
  const PRIZES_STORAGE_KEY = wheelId ? `lucky-wheel-prizes-${wheelId}` : 'lucky-wheel-prizes';
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [rotating, setRotating] = useState(false);
  const [currentRotation, setCurrentRotation] = useState(0);
  const [imagesLoaded, setImagesLoaded] = useState<{[key: string]: HTMLImageElement}>({});
  const [wheelSize, setWheelSize] = useState(600); // 默认大小为600
  const [isFullScreen, setIsFullScreen] = useState(false)
  const [result, setResult] = useState<Prize | null>(null)
  const [showSettings, setShowSettings] = useState(false);
  const [lastAddedIndex, setLastAddedIndex] = useState<number | null>(null);

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!document.fullscreenElement;
      setIsFullScreen(isCurrentlyFullscreen);

      // 强制重新计算转盘尺寸
      setTimeout(() => {
        const updateEvent = new Event('resize');
        window.dispatchEvent(updateEvent);
      }, 100);
    };

    // 添加多个浏览器兼容的事件监听器
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // 计算转盘尺寸
  useEffect(() => {
    const updateSize = () => {
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      let newSize;

      if (isFullScreen) {
        // 全屏模式下，更精确地计算可用空间
        const titleHeight = 60; // 标题高度
        const buttonsHeight = 60; // 按钮区域高度
        const padding = 40; // 上下内边距
        const availableHeight = viewportHeight - titleHeight - buttonsHeight - padding;
        const availableWidth = viewportWidth - 40; // 左右边距

        // 计算合适的尺寸，确保能在一屏内显示
        newSize = Math.min(availableWidth * 0.9, availableHeight * 0.9);
        // 确保最小尺寸
        newSize = Math.max(newSize, 300);
        // 确保最大尺寸合理
        newSize = Math.min(newSize, 600);
      } else {
        // 非全屏模式
        if (viewportWidth <= 640) { // sm
          newSize = 400;
        } else {
          newSize = 600;
        }
      }
      setWheelSize(newSize);
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [isFullScreen, wheelSize]);

  useEffect(() => {
    const loadImages = async () => {
      const images: { [key: string]: HTMLImageElement } = {};
    
      for (const prize of prizes) {
        if (prize.image) {
          try {
            // 创建新图片对象
            const img = new window.Image();
            
            // 为非base64图片设置crossOrigin
            if (!prize.image.startsWith('data:')) {
              img.crossOrigin = "anonymous";
            }
            
            // 设置加载事件处理
            const imageLoaded = new Promise<void>((resolve, reject) => {
              img.onload = () => resolve();
              img.onerror = (error) => reject(error);
            });
            
            // 设置图片源
            img.src = prize.image;
            
            // 等待图片加载完成
            await imageLoaded;
            
            // 存储加载成功的图片
            images[prize.image] = img;
          } catch (error) {
            console.error('加载图片时出错:', error);
          }
        }
      }
    
      setImagesLoaded(prevImages => {
        // 合并新旧图片缓存，确保不丢失已加载的图片
        return { ...prevImages, ...images };
      });
    };
    
    loadImages();
  }, [prizes]);

  // 初始化时从localStorage加载保存的选项（仅在没有传入prizes时）
  useEffect(() => {
    // 如果已经有prizes传入，不从localStorage加载
    if (prizes && prizes.length > 0) {
      return;
    }

    const savedPrizes = localStorage.getItem(PRIZES_STORAGE_KEY);
    if (savedPrizes) {
      try {
        const parsedPrizes = JSON.parse(savedPrizes) as Prize[];

        const validPrizes = parsedPrizes.map(prize => ({
          ...prize,
          image: prize.image || undefined
        }));
        onPrizesChange?.(validPrizes);
      } catch (e) {
        console.error('解析保存的奖品失败:', e);
      }
    }
  }, [onPrizesChange, prizes, PRIZES_STORAGE_KEY]);

  // 当选项变化时保存到localStorage
  const handlePrizesChange = (newPrizes: Prize[]) => {
    localStorage.setItem(PRIZES_STORAGE_KEY, JSON.stringify(newPrizes));
    onPrizesChange?.(newPrizes);
  };

  // 绘制转盘
  const drawWheel = useCallback((rotation: number = 0) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d', {
      alpha: true,
      antialias: true,
    }) as CanvasRenderingContext2D;
    if (!ctx) return;

    // 获取设备像素比
    const dpr = window.devicePixelRatio || 1;

    // 使用 wheelSize
    if (wheelSize === 0) return; // 如果尺寸还未计算，先返回

    // 设置实际渲染尺寸
    canvas.width = wheelSize * dpr;
    canvas.height = wheelSize * dpr;

    // 设置显示尺寸
    canvas.style.width = `${wheelSize}px`;
    canvas.style.height = `${wheelSize}px`;

    // 缩放上下文以匹配设备像素比
    ctx.scale(dpr, dpr);

    const center = wheelSize / 2;
    const radius = wheelSize / 2 - 20;

    // 启用抗锯齿
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // 设置阴影效果
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'; // 阴影颜色
    ctx.shadowBlur = 15; // 阴影模糊程度
    ctx.shadowOffsetX = 0; // 阴影水平偏移
    ctx.shadowOffsetY = 2; // 阴影垂直偏移

    // 清空画布
    ctx.clearRect(0, 0, wheelSize, wheelSize);

    // 保存当前状态
    ctx.save();
    ctx.translate(center, center);
    ctx.rotate((rotation * Math.PI) / 180);

    const anglePerPrize = (Math.PI * 2) / prizes.length;
    
    // 计算文字可用空间，考虑开始按钮
    const textStartRadius = radius - 20; // 文字起始位置（距离边缘20px

    // 计算扇形区域的实际可用宽度
    const sectorWidth = Math.PI * radius / prizes.length; // 每个扇形在圆弧上的宽度

    // 获取最长文本的像素宽度
    ctx.font = `bold 16px "PingFang SC", "Microsoft YaHei", sans-serif`;
    const maxTextWidth = Math.max(...prizes.map(prize => ctx.measureText(prize.text).width));
    
    // 计算缩放比例
    const scaleFactor = Math.min(1, sectorWidth / (maxTextWidth / 3));
    const fontSize = Math.max(6, Math.floor(22 * scaleFactor)); // 最小字号12px

    // 计算图片尺寸，基于扇形区域的高度
    const sectorHeight = radius - 20; // 从边缘到按钮的可用高度
    const imgSize = Math.max(
      20,
      sectorWidth > (wheelSize/5) ? (wheelSize/5) : sectorWidth  // 最大不超过可用宽度的40%
    );

    // 绘制扇形和文字
    prizes.forEach((prize, index) => {
      const midAngle = (-Math.PI/2) + index * anglePerPrize;

      const startAngle = midAngle - anglePerPrize/2;
      const endAngle = midAngle + anglePerPrize/2;

      // 绘制扇形
      ctx.beginPath();
      ctx.moveTo(0, 0);
      ctx.arc(0, 0, radius, startAngle, endAngle);
      ctx.closePath();
      ctx.fillStyle = prize.color;
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.lineWidth = 1;
      ctx.fill();
      ctx.stroke();

      // 保存状态并旋转到正确位置
      ctx.save();
      ctx.rotate(startAngle + anglePerPrize / 2);

      // 设置文字样式
      ctx.textAlign = 'right';
      ctx.textBaseline = 'middle';
      ctx.fillStyle = '#ffffff';
      ctx.font = `bold ${fontSize}px "PingFang SC", "Microsoft YaHei", sans-serif`;

      // 计算文字和图片位置
      const startX = textStartRadius;
      
      // 如果有图片，先绘制图片
      if (prize.image && imagesLoaded[prize.image]) {
        const img = imagesLoaded[prize.image];
        const imgX = startX - imgSize;
        const imgY = -imgSize / 2;

        try {
          ctx.save();
          ctx.beginPath();
          ctx.arc(imgX + imgSize/2, imgY + imgSize/2, imgSize/2, 0, Math.PI * 2);
          ctx.clip();
          ctx.drawImage(img, imgX, imgY, imgSize, imgSize);
          ctx.restore();
        } catch (error) {
          console.error('绘制图片时出错:', error);
        }
      }

      // 计算文字可用长度（在绘制文字之前）
      const currentImgSize = prize.image ? imgSize + 10 : 0; // 如果有图片，加上间距
      const textAvailableLength = sectorHeight - currentImgSize - startButtonSize / 2 - 10; // 从扇形高度减去图片（如果有的话）

      // 如果文字长度超过了可用长度，则截取文字并添加...
      const text = prize.text;
      let truncatedText = text; // 在这里定义并初始化

      if (ctx.measureText(text).width > textAvailableLength) {
        const ellipsis = '...';
        
        // 二分查找合适的截断位置
        let start = 0;
        let end = text.length;
        
        while (start < end) {
          const mid = Math.floor((start + end + 1) / 2);
          const testText = text.slice(0, mid) + ellipsis;
          
          if (ctx.measureText(testText).width <= textAvailableLength) {
            start = mid;
          } else {
            end = mid - 1;
          }
        }
        
        // 确保至少显示一个字符
        const finalLength = Math.max(1, start);
        truncatedText = text.slice(0, finalLength) + ellipsis;
      }

      // 绘制单行文字
      const textX = startX - currentImgSize;
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
      ctx.lineWidth = Math.max(2, 3 * scaleFactor);
      ctx.strokeText(truncatedText, textX, 0);
      ctx.fillText(truncatedText, textX, 0);

      ctx.restore();
    });

    // 恢复状态
    ctx.restore();

    // 绘制外边框
    // ctx.beginPath();
    // ctx.arc(center, center, radius + 10, 0, Math.PI * 2);
    // ctx.strokeStyle = '#1d4ed8';
    // ctx.lineWidth = 8;
    // ctx.stroke();

    // 绘制内边框
    ctx.beginPath();
    ctx.arc(center, center, radius, 0, Math.PI * 2);
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 2;
    ctx.stroke();
  }, [prizes, wheelSize, imagesLoaded]);

  // 分开处理旋转更新，添加imagesLoaded作为依赖
  useEffect(() => {
    if (Object.keys(imagesLoaded).length > 0 || prizes.every(prize => !prize.image)) {
      drawWheel(currentRotation);
    }
  }, [drawWheel, currentRotation, imagesLoaded, prizes]);

  // 确保在全屏状态变化后重绘转盘
  useEffect(() => {
    // 延迟重绘，确保DOM更新完成
    const timer = setTimeout(() => {
      drawWheel(currentRotation);
    }, 200);

    return () => clearTimeout(timer);
  }, [isFullScreen, drawWheel, currentRotation]);

  // 开始旋转
  const startRotation = () => {
    if (rotating) return;
    const selectedIndex = Math.floor(Math.random() * prizes.length);
    // setSelectedIndex(selectedIndex);
    
    setRotating(true);
    
    // 增加延迟以确保角度重置被应用
    setTimeout(() => {
      const extraSpins = 8 + Math.random() * 3; // 随机旋转8-11圈
      const sectorAngle = 360 / prizes.length;
      
      // 获取extraSpins的整数和小数部分
      // 获取整数部分
      const wholePart = Math.floor(extraSpins);
      // 获取小数部分
      const decimalPart = extraSpins - wholePart;

      // 计算目标角度
      // 1. 首先计算选中项的中心角度（在标准坐标系中）
      const decimalPartAngle = sectorAngle / 2 - sectorAngle * decimalPart;
      const selectedAngleFromRight = -selectedIndex * sectorAngle + decimalPartAngle;
      // 2. 从右侧开始（0度），顺时针旋转到顶部需要270度
      // 3. 然后减去所选项的角度，这样它会停在顶部
      const targetAngle = 360 * wholePart + selectedAngleFromRight;
      
      // 动画旋转
      let start: number | null = null;
      const duration = 8000; // 5秒

      const animate = (timestamp: number) => {
        if (!start) start = timestamp;
        const progress = (timestamp - start) / duration;

        if (progress < 1) {
          const currentAngle = easeOut(progress) * targetAngle;
          setCurrentRotation(currentAngle);
          requestAnimationFrame(animate);
        } else {
          setCurrentRotation(targetAngle % 360); // 只保留余数，避免大数值
          setRotating(false);
          setResult(prizes[selectedIndex]);
        }
      };

      requestAnimationFrame(animate);
    }, 50);
  };

  // 缓动函数
  const easeOut = (x: number): number => {
    return 1 - Math.pow(1 - x, 3);
  };

  // 全屏功能
  const toggleFullscreen = () => {
    const element = document.getElementById('wheelContainer'); // 获取转盘的祖先元素
    if (element) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
        // 不需要手动设置状态，fullscreenchange事件会处理
      } else {
        element.requestFullscreen();
        // 不需要手动设置状态，fullscreenchange事件会处理
      }
    }
  };

  // 更新所有使用 onPrizesChange 的地方
  const handleAddPrize = () => {
    const newPrize: Prize = {
      text: '',
      color: "#" + Math.floor(Math.random()*16777215).toString(16),
    };
    handlePrizesChange([...prizes, newPrize]);
    setLastAddedIndex(prizes.length);
  };

  const handleDeletePrize = (index: number) => {
    const newPrizes = prizes.filter((_, i) => i !== index);
    handlePrizesChange(newPrizes);
  };

  const handleEditPrize = (index: number, field: keyof Prize, value: string) => {
    const newPrizes = [...prizes];
    newPrizes[index] = { ...newPrizes[index], [field]: value };
    handlePrizesChange(newPrizes);
  };

  // 自动聚焦新添加的输入框
  useEffect(() => {
    if (lastAddedIndex !== null) {
      const input = document.querySelector(`input[data-index="${lastAddedIndex}"]`) as HTMLInputElement;
      input?.focus();
      setLastAddedIndex(null);
    }
  }, [lastAddedIndex]);

  // 处理图片上传
  const handleImageUpload = (index: number, event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        // 直接调用handlePrizesChange而不是handleEditPrize
        const newPrizes = [...prizes];
        newPrizes[index] = { ...newPrizes[index], image: imageUrl };
        handlePrizesChange(newPrizes);
      };
      reader.readAsDataURL(file);
    }
  };

  if (isFullScreen) {
    return (
      <>
        {/* 全屏模式的完整布局 */}
        <div className="fixed inset-0 bg-white dark:bg-gray-900 z-40 flex flex-col">
          {/* 标题区域 */}
          {title && (
            <div className="flex-shrink-0 p-4 text-center">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100">{title}</h1>
            </div>
          )}

          {/* 转盘区域 */}
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="relative">
              <div
                className={`${styles.container}`}
                style={{
                  '--wheel-size': `${wheelSize}px`
                } as React.CSSProperties}
              >
                <div className={styles.indicator} />
                <div className={styles.wheelWrapper}>
                  <canvas
                    ref={canvasRef}
                    className={styles.wheel}
                  />

                  <button
                    className={styles.startButton}
                    onClick={startRotation}
                    disabled={rotating}
                    style={{
                      '--start-button-size': `${startButtonSize}px`
                    } as React.CSSProperties}
                  >
                    Start
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex gap-2 justify-center p-4 flex-shrink-0">
            <button
              onClick={toggleFullscreen}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15" />
              </svg>
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
          </div>

          {/* 全屏模式下的设置面板 */}
          <div
            className={`fixed inset-y-0 right-0 w-96 bg-white dark:bg-gray-800 shadow-2xl border-l border-gray-200 dark:border-gray-700 z-50 transition-transform duration-300 ease-in-out ${
              showSettings ? 'translate-x-0' : 'translate-x-full'
            }`}
          >
            <div className="h-full flex flex-col">
              <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">Options Setting</h2>
                <button
                  onClick={() => setShowSettings(false)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="flex-1 overflow-auto p-4">
                <div className="space-y-4">
                  {prizes.map((prize, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="relative">
                            <input
                              type="color"
                              value={prize.color}
                              onChange={(e) => handleEditPrize(index, 'color', e.target.value)}
                              className="absolute opacity-0 w-4 h-4 cursor-pointer"
                            />
                            <div
                              className="w-4 h-4 rounded-full border border-gray-200"
                              style={{ backgroundColor: prize.color }}
                            />
                          </div>
                          <input
                            type="text"
                            value={prize.text}
                            onChange={(e) => handleEditPrize(index, 'text', e.target.value)}
                            placeholder="Input Option"
                            className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                            data-index={index}
                          />
                        </div>
                        <button
                          onClick={() => handleDeletePrize(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>

                      <div className="mt-2">
                        <div className="flex items-center gap-2">
                          {prize.image && (
                            <div className="relative group">
                              <Image
                                src={prize.image}
                                alt="prize"
                                width={64}
                                height={64}
                                className="object-cover rounded"
                              />
                              <button
                                onClick={() => handleEditPrize(index, 'image', '')}
                                className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          )}
                          <label className="flex items-center gap-2 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <input
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={(e) => handleImageUpload(index, e)}
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
                <button
                  onClick={handleAddPrize}
                  className="w-full py-2 px-4 bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  New Option
                </button>
              </div>
            </div>
          </div>
        </div>

        <SelectedModal showModal={result!=null} result={result} onClose={() => setResult(null)} />
      </>
    );
  }

  // 普通模式的布局
  return (
    <>
        <div className="relative">
          <div className={`relative bg-white dark:bg-gray-900 overflow-hidden ${showSettings ? 'pr-[300px]' : ''} transition-all duration-300 ease-in-out`}>
            <div className="flex justify-center">
              <div
                className={`${styles.container}`}
                style={{
                  '--wheel-size': `${wheelSize}px`
                } as React.CSSProperties}
              >
                <div className={styles.indicator} />
                <div className={styles.wheelWrapper}>
                  <canvas
                    ref={canvasRef}
                    className={styles.wheel}
                  />

                  <button
                    className={styles.startButton}
                    onClick={startRotation}
                    disabled={rotating}
                    style={{
                      '--start-button-size': `${startButtonSize}px`
                    } as React.CSSProperties}
                  >
                    Start
                  </button>
                </div>
              </div>
            </div>

            {/* 普通模式下的设置面板 */}
            <div
              className={`absolute right-0 top-0 transition-all duration-300 ease-in-out z-50 ${
                showSettings ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full invisible'
              }`}
              style={{
                height: `${wheelSize}px`,
                width: '300px',
                marginTop: `${(wheelSize * 0.1)}px`
              }}
            >
              <div className="h-full bg-white dark:bg-gray-800 shadow-lg rounded-lg flex flex-col border border-gray-200 dark:border-gray-700">
              <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">Options Setting</h2>
                <button
                  onClick={() => setShowSettings(false)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="flex-1 overflow-auto p-4">
                <div className="space-y-4">
                  {prizes.map((prize, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          {/* 颜色选择器 */}
                          <div className="relative">
                            <input
                              type="color"
                              value={prize.color}
                              onChange={(e) => handleEditPrize(index, 'color', e.target.value)}
                              className="absolute opacity-0 w-4 h-4 cursor-pointer"
                            />
                            <div 
                              className="w-4 h-4 rounded-full border border-gray-200"
                              style={{ backgroundColor: prize.color }}
                            />
                          </div>
                          {/* 文本输入 */}
                          <input
                            type="text"
                            value={prize.text}
                            onChange={(e) => handleEditPrize(index, 'text', e.target.value)}
                            placeholder="Input Option"
                            className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                            data-index={index}
                          />
                        </div>
                        <button
                          onClick={() => handleDeletePrize(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>

                      {/* 图片上传区域 */}
                      <div className="mt-2">
                        <div className="flex items-center gap-2">
                          {prize.image && (
                            <div className="relative group">
                              <Image
                                src={prize.image}
                                alt="prize"
                                width={64}
                                height={64}
                                className="object-cover rounded"
                              />
                              <button
                                onClick={() => handleEditPrize(index, 'image', '')}
                                className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          )}
                          <label className="flex items-center gap-2 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <input
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={(e) => handleImageUpload(index, e)}
                            />
                          </label>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
                <button
                  onClick={handleAddPrize}
                  className="w-full py-2 px-4 bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  New Option
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 普通模式下的按钮 */}
          <div className="flex gap-2 justify-center mt-4">
            <button
              onClick={toggleFullscreen}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15" />
              </svg>
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-gray-700 dark:text-gray-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-5">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
          </div>
        </div>

      <SelectedModal showModal={result!=null} result={result} onClose={() => setResult(null)} />
    </>
  );
}