import type { EnvironmentConfig } from '~/types'

/**
 * 获取环境配置
 * 根据当前环境返回相应的配置
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const runtimeConfig = useRuntimeConfig()
  
  // 检查是否为开发环境
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isProduction = process.env.NODE_ENV === 'production'
  
  // 获取API基础URL
  let apiBaseUrl: string
  
  if (isDevelopment) {
    // 开发环境使用本地API
    apiBaseUrl = runtimeConfig.public.apiBaseUrl || 'http://localhost:3000/api'
  } else {
    // 生产环境使用环境变量或默认值
    apiBaseUrl = runtimeConfig.public.apiBaseUrl || 'https://decisionsmaker.online/api'
  }
  
  return {
    apiBaseUrl,
    isDevelopment,
    isProduction
  }
}

/**
 * 获取API端点配置
 */
export function getApiEndpoints() {
  const config = getEnvironmentConfig()
  
  return {
    // 获取转盘页面数据（统一接口）
    wheelPage: (slug?: string) => {
      const endpoint = slug ? `/wheel-page/${slug}` : '/wheel-page'
      return `${config.apiBaseUrl}${endpoint}`
    },
    
    // 获取默认奖品
    prizes: () => `${config.apiBaseUrl}/prizes`,
    
    // 获取转盘列表
    wheelList: () => `${config.apiBaseUrl}/wheel-list`,
    
    // 重新验证页面
    revalidate: () => `${config.apiBaseUrl}/revalidate`
  }
}

/**
 * 请求配置
 */
export const requestConfig = {
  // 默认请求头
  defaultHeaders: {
    'Content-Type': 'application/json',
  },
  
  // 请求超时时间（毫秒）
  timeout: 10000,
  
  // 重试次数
  retryCount: 3,
}

/**
 * 应用配置
 */
export const appConfig = {
  // 应用名称
  name: 'DecisionsMaker Online',
  
  // 应用版本
  version: '2.0.0',
  
  // 默认语言
  defaultLocale: 'en',
  
  // 支持的语言
  supportedLocales: ['en', 'zh'],
  
  // 默认主题
  defaultTheme: 'system' as const,
  
  // SEO配置
  seo: {
    siteName: 'DecisionsMaker Online',
    siteUrl: 'https://decisionsmaker.online',
    defaultTitle: 'DecisionsMaker Online - Smart Decision Making Wheel',
    defaultDescription: 'Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly.',
    defaultKeywords: 'decision making, decision wheel, random choice generator, decisionsmaker online, online decision tool',
    twitterHandle: '@decisionsmaker',
    ogImage: '/og-image.png'
  },
  
  // 分析配置
  analytics: {
    googleAnalyticsId: 'G-36JS659442',
    baiduAnalyticsId: 'b209671ea8fa33b06ef52e4025885a97'
  },
  
  // 功能开关
  features: {
    enableAnalytics: true,
    enableServiceWorker: false,
    enablePWA: false,
    enableI18n: false
  }
}

/**
 * 开发环境配置
 */
export const devConfig = {
  // 是否显示调试信息
  showDebugInfo: true,
  
  // 是否启用热重载
  enableHotReload: true,
  
  // 是否显示性能指标
  showPerformanceMetrics: false
}

/**
 * 生产环境配置
 */
export const prodConfig = {
  // 是否启用压缩
  enableCompression: true,
  
  // 是否启用缓存
  enableCaching: true,
  
  // 缓存时间（秒）
  cacheMaxAge: 3600,
  
  // 是否启用CDN
  enableCDN: false
}
