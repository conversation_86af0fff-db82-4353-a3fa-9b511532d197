# 内容更新指南

## 🎯 概述

你的应用现在使用 **增量静态再生（ISR）+ 按需重新验证** 的方案，既保持了SEO优势，又能及时更新内容。

## 🔧 工作原理

### 自动更新
- 页面每 **60秒** 自动检查是否需要更新
- 用户访问过期页面时，会在后台触发重新生成
- 用户仍然看到缓存版本，下次访问时看到新内容

### 手动更新
- 通过管理界面立即触发特定页面更新
- 通过API调用批量更新页面
- 可以集成到你的后端系统中

## 📋 使用方法

### 方法1：管理界面（推荐）

访问：`https://yourdomain.com/admin/revalidate`

功能：
- ✅ 重新验证所有页面
- ✅ 重新验证首页
- ✅ 重新验证特定页面
- ✅ 快速按钮重新验证常用页面

### 方法2：API调用

#### 重新验证所有页面
```bash
curl -X POST https://yourdomain.com/api/revalidate \
  -H "Content-Type: application/json" \
  -d '{"secret": "your-secret-key"}'
```

#### 重新验证特定页面
```bash
curl -X POST https://yourdomain.com/api/revalidate \
  -H "Content-Type: application/json" \
  -d '{
    "path": "/what-to-eat-decision-wheel",
    "secret": "your-secret-key"
  }'
```

#### 简单GET请求
```bash
curl "https://yourdomain.com/api/revalidate?path=/what-to-eat-decision-wheel&secret=your-secret-key"
```

### 方法3：后端集成

在你的后端API中，当内容更新时自动触发重新验证：

```javascript
// Node.js 示例
async function updateWheelContent(slug, newContent) {
  try {
    // 1. 更新数据库中的内容
    await database.updateWheel(slug, newContent);
    
    // 2. 触发前端页面重新验证
    const response = await fetch('https://yourdomain.com/api/revalidate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        path: `/${slug}`,
        secret: process.env.REVALIDATE_SECRET 
      })
    });
    
    if (response.ok) {
      console.log(`✅ 页面 ${slug} 已触发更新`);
    }
  } catch (error) {
    console.error('更新失败:', error);
  }
}

// PHP 示例
function updateWheelContent($slug, $newContent) {
    // 1. 更新数据库
    updateDatabase($slug, $newContent);
    
    // 2. 触发重新验证
    $data = json_encode([
        'path' => "/$slug",
        'secret' => $_ENV['REVALIDATE_SECRET']
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $data
        ]
    ]);
    
    file_get_contents('https://yourdomain.com/api/revalidate', false, $context);
}
```

## 🚀 部署配置

### Vercel环境变量

在Vercel Dashboard中添加：

```bash
# 重新验证密钥（必须）
REVALIDATE_SECRET=your-production-secret-key-here

# API配置（已有）
NEXT_PUBLIC_API_BASE_URL=https://api.decisionsmaker.online/api
NEXT_PUBLIC_SITE_URL=https://www.decisionsmaker.online
```

### 安全建议

1. **使用强密钥**：生产环境中使用复杂的随机字符串作为 `REVALIDATE_SECRET`
2. **限制访问**：考虑为管理界面添加身份验证
3. **监控调用**：记录重新验证API的调用日志

## 📊 页面列表

当前支持的页面路径：

- `/` - 首页
- `/what-to-eat-decision-wheel` - 美食决策转盘
- `/yes-no-decision-maker` - 是否决策器
- `/weekend-activity-planner` - 周末活动规划器
- `/team-member-picker` - 团队成员选择器

## 🔍 故障排除

### 问题：重新验证后内容没有更新

**解决方案：**
1. 检查API是否返回最新数据
2. 清除浏览器缓存
3. 等待1-2分钟（CDN缓存更新）
4. 检查重新验证API是否成功调用

### 问题：管理界面无法访问

**解决方案：**
1. 确保路径正确：`/admin/revalidate`
2. 检查部署是否成功
3. 查看浏览器控制台错误信息

### 问题：API调用返回401错误

**解决方案：**
1. 检查 `REVALIDATE_SECRET` 环境变量是否正确设置
2. 确保请求中包含正确的secret参数

## 📈 最佳实践

1. **内容更新后立即重新验证**：在后端更新内容后立即调用重新验证API
2. **批量更新**：如果同时更新多个页面，可以调用重新验证所有页面
3. **监控性能**：定期检查页面加载速度和缓存命中率
4. **备份策略**：重要内容更新前先备份

## 🎉 总结

现在你的应用具有：
- ✅ **SEO友好**：静态生成保证搜索引擎优化
- ✅ **内容实时性**：ISR确保内容及时更新
- ✅ **性能优异**：缓存机制保证快速加载
- ✅ **管理便捷**：多种方式触发内容更新
- ✅ **自动化支持**：可集成到现有工作流程

这是一个完美的平衡方案！🚀
