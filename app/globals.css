@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden; /* 防止横向滚动 */
}

html {
  overflow-x: hidden; /* 防止横向滚动 */
}

/* 富文本内容暗黑模式样式 - 使用更强的选择器和!important */
.rich-text-content p,
.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3,
.rich-text-content h4,
.rich-text-content h5,
.rich-text-content h6,
.rich-text-content li,
.rich-text-content span,
.rich-text-content div {
  color: rgb(55 65 81) !important; /* text-gray-700 */
}

.dark .rich-text-content p,
.dark .rich-text-content h1,
.dark .rich-text-content h2,
.dark .rich-text-content h3,
.dark .rich-text-content h4,
.dark .rich-text-content h5,
.dark .rich-text-content h6,
.dark .rich-text-content li,
.dark .rich-text-content span,
.dark .rich-text-content div {
  color: rgb(209 213 219) !important; /* text-gray-300 */
}

.rich-text-content strong,
.rich-text-content b {
  color: rgb(17 24 39) !important; /* text-gray-900 */
  font-weight: 600 !important;
}

.dark .rich-text-content strong,
.dark .rich-text-content b {
  color: rgb(243 244 246) !important; /* text-gray-100 */
}

.rich-text-content a {
  color: rgb(37 99 235) !important; /* text-blue-600 */
  text-decoration: underline !important;
}

.dark .rich-text-content a {
  color: rgb(96 165 250) !important; /* text-blue-400 */
}

.rich-text-content a:hover {
  color: rgb(29 78 216) !important; /* text-blue-800 */
}

.dark .rich-text-content a:hover {
  color: rgb(147 197 253) !important; /* text-blue-300 */
}

.rich-text-content blockquote {
  border-left: 4px solid rgb(209 213 219) !important; /* border-gray-300 */
  padding-left: 1rem !important;
  font-style: italic !important;
  color: rgb(55 65 81) !important; /* text-gray-700 */
}

.dark .rich-text-content blockquote {
  border-left-color: rgb(75 85 99) !important; /* border-gray-600 */
  color: rgb(209 213 219) !important; /* text-gray-300 */
}

.rich-text-content code {
  background-color: rgb(243 244 246) !important; /* bg-gray-100 */
  color: rgb(17 24 39) !important; /* text-gray-900 */
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.875rem !important;
}

.dark .rich-text-content code {
  background-color: rgb(55 65 81) !important; /* bg-gray-700 */
  color: rgb(243 244 246) !important; /* text-gray-100 */
}

.rich-text-content pre {
  background-color: rgb(243 244 246) !important; /* bg-gray-100 */
  color: rgb(17 24 39) !important; /* text-gray-900 */
  padding: 1rem !important;
  border-radius: 0.25rem !important;
  overflow-x: auto !important;
}

.dark .rich-text-content pre {
  background-color: rgb(31 41 55) !important; /* bg-gray-800 */
  color: rgb(243 244 246) !important; /* text-gray-100 */
}

.rich-text-content ul,
.rich-text-content ol {
  /* margin-left: 1.5rem !important; */
  margin-bottom: 1rem !important;
}

.rich-text-content ul {
  list-style-type: disc !important;
}

.rich-text-content ol {
  list-style-type: decimal !important;
}

.rich-text-content li {
  margin-bottom: 0.25rem !important;
}

.rich-text-content p {
  margin-bottom: 1rem !important;
}

.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3,
.rich-text-content h4,
.rich-text-content h5,
.rich-text-content h6 {
  font-weight: bold !important;
  margin-bottom: 0.75rem !important;
  color: rgb(17 24 39) !important; /* text-gray-900 */
}

.dark .rich-text-content h1,
.dark .rich-text-content h2,
.dark .rich-text-content h3,
.dark .rich-text-content h4,
.dark .rich-text-content h5,
.dark .rich-text-content h6 {
  color: rgb(243 244 246) !important; /* text-gray-100 */
}

.rich-text-content h1 { font-size: 1.5rem !important; }
.rich-text-content h2 { font-size: 1.25rem !important; }
.rich-text-content h3 { font-size: 1.125rem !important; }

.rich-text-content table {
  width: 100% !important;
  border-collapse: collapse !important;
  border: 1px solid rgb(209 213 219) !important; /* border-gray-300 */
  margin-bottom: 1rem !important;
}

.dark .rich-text-content table {
  border-color: rgb(75 85 99) !important; /* border-gray-600 */
}

.rich-text-content th,
.rich-text-content td {
  border: 1px solid rgb(209 213 219) !important; /* border-gray-300 */
  padding: 0.75rem !important;
  text-align: left !important;
}

.dark .rich-text-content th,
.dark .rich-text-content td {
  border-color: rgb(75 85 99) !important; /* border-gray-600 */
}

.rich-text-content th {
  background-color: rgb(243 244 246) !important; /* bg-gray-100 */
  font-weight: 600 !important;
  color: rgb(17 24 39) !important; /* text-gray-900 */
}

.dark .rich-text-content th {
  background-color: rgb(55 65 81) !important; /* bg-gray-700 */
  color: rgb(243 244 246) !important; /* text-gray-100 */
}

.rich-text-content td {
  color: rgb(55 65 81) !important; /* text-gray-700 */
}

.dark .rich-text-content td {
  color: rgb(209 213 219) !important; /* text-gray-300 */
}

/* 处理内联类的特殊情况 */
.dark .rich-text-content .list-disc,
.dark .rich-text-content .list-inside,
.dark .rich-text-content .space-y-2 {
  color: rgb(209 213 219) !important; /* text-gray-300 */
}

.dark .rich-text-content .list-disc li,
.dark .rich-text-content .list-inside li,
.dark .rich-text-content .space-y-2 li {
  color: rgb(209 213 219) !important; /* text-gray-300 */
}

/* 强制覆盖所有内联样式 - 包括背景色 */
.dark .rich-text-content *,
.dark .rich-text-content *[style] {
  color: rgb(209 213 219) !important; /* text-gray-300 */
  background-color: transparent !important;
  background: transparent !important;
}

.dark .rich-text-content h1,
.dark .rich-text-content h1[style],
.dark .rich-text-content h2,
.dark .rich-text-content h2[style],
.dark .rich-text-content h3,
.dark .rich-text-content h3[style],
.dark .rich-text-content h4,
.dark .rich-text-content h4[style],
.dark .rich-text-content h5,
.dark .rich-text-content h5[style],
.dark .rich-text-content h6,
.dark .rich-text-content h6[style] {
  color: rgb(243 244 246) !important; /* text-gray-100 */
  background-color: transparent !important;
  background: transparent !important;
}

.dark .rich-text-content strong,
.dark .rich-text-content strong[style],
.dark .rich-text-content b,
.dark .rich-text-content b[style] {
  color: rgb(243 244 246) !important; /* text-gray-100 */
  background-color: transparent !important;
  background: transparent !important;
}

.dark .rich-text-content a,
.dark .rich-text-content a[style] {
  color: rgb(96 165 250) !important; /* text-blue-400 */
  background-color: transparent !important;
  background: transparent !important;
}

.dark .rich-text-content p,
.dark .rich-text-content p[style],
.dark .rich-text-content div,
.dark .rich-text-content div[style],
.dark .rich-text-content span,
.dark .rich-text-content span[style],
.dark .rich-text-content li,
.dark .rich-text-content li[style] {
  color: rgb(209 213 219) !important; /* text-gray-300 */
  background-color: transparent !important;
  background: transparent !important;
}

/* 特别处理可能有白色背景的元素 */
.dark .rich-text-content [style*="background"],
.dark .rich-text-content [style*="background-color"],
.dark .rich-text-content [style*="background:#fff"],
.dark .rich-text-content [style*="background:#ffffff"],
.dark .rich-text-content [style*="background:white"],
.dark .rich-text-content [style*="background-color:#fff"],
.dark .rich-text-content [style*="background-color:#ffffff"],
.dark .rich-text-content [style*="background-color:white"],
.dark .rich-text-content [style*="background-color: #fff"],
.dark .rich-text-content [style*="background-color: #ffffff"],
.dark .rich-text-content [style*="background-color: white"] {
  background-color: transparent !important;
  background: transparent !important;
}

/* 强制覆盖富文本容器本身的背景 */
.dark .rich-text-content {
  background-color: transparent !important;
  background: transparent !important;
}

/* 处理可能的嵌套容器 */
.dark .rich-text-content > *,
.dark .rich-text-content > * > *,
.dark .rich-text-content > * > * > * {
  background-color: transparent !important;
  background: transparent !important;
}

/* 转盘样式 */
.wheel-container {
  @apply relative w-[300px] h-[300px] sm:w-[400px] sm:h-[400px];
}

.wheel-pointer {
  @apply absolute -top-6 left-1/2 -translate-x-1/2 w-0 h-0 
         border-l-[15px] border-l-transparent
         border-r-[15px] border-r-transparent
         border-t-[20px] border-t-red-500
         z-20;
}

.wheel-border {
  @apply absolute inset-0 rounded-full border-8 border-blue-600 dark:border-blue-400;
}

.wheel-spinner {
  @apply absolute inset-0 rounded-full overflow-hidden transition-all duration-[3000ms] ease-out;
}

.wheel-segment {
  @apply absolute w-1/2 h-full left-1/2 top-0;
  transform-origin: 0% 50%;
  clip-path: polygon(0 0, 100% 0, 0 100%);
}

.segment-even {
  @apply bg-blue-300 dark:bg-blue-700;
}

.segment-odd {
  @apply bg-blue-400 dark:bg-blue-600;
}

.wheel-option {
  @apply absolute w-1/2 h-full left-1/2 top-0;
  transform-origin: 0% 50%;
}

.option-text-container {
  @apply absolute w-full text-center;
  top: 25%;
  transform: translateX(-50%) translateY(-50%);
}

.option-text-container span {
  @apply inline-block text-base font-bold text-gray-800 dark:text-white 
         bg-white/30 dark:bg-black/30 px-2 py-1 rounded;
  transform: rotate(90deg);
}

.spin-button {
  @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 
         w-16 h-16 rounded-full bg-blue-600 hover:bg-blue-700 
         text-white font-bold shadow-lg transform transition-all 
         active:scale-95 disabled:bg-gray-400 z-10;
}


/* components/LuckWheel.module.css */
.wheelWrapper {
  position: relative;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  overflow: hidden;
}

.wheel {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0s; /* 会在JS中动态设置 */
}

.prize {
  position: absolute;
  top: 0;
  left: 50%;
  width: 50%;
  height: 50%;
  transform-origin: 0% 100%;
}

.prizeContent {
  position: absolute;
  left: -100%;
  width: 200%;
  height: 200%;
  transform: skewY(-30deg) rotate(-15deg);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.prizeImage {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
}

.prizeText {
  color: white;
  font-size: 14px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  transform: rotate(-10deg);
}

.indicator {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 40px solid #ff4d4f;
  z-index: 3;
}
