"use client";

import { useEffect, useRef } from 'react';

interface RichTextContentProps {
  content: string;
  className?: string;
}

export default function RichTextContent({ content, className = "" }: RichTextContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 清理内联样式，特别是背景色相关的样式
    if (contentRef.current) {
      const elements = contentRef.current.querySelectorAll('*');
      elements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        if (htmlElement.style) {
          // 移除背景相关的内联样式
          htmlElement.style.removeProperty('background');
          htmlElement.style.removeProperty('background-color');
          htmlElement.style.removeProperty('backgroundColor');
          // 也可以移除颜色，让CSS接管
          // htmlElement.style.removeProperty('color');
        }
      });
    }
  }, [content]);

  return (
    <div
      ref={contentRef}
      className={`
        rich-text-content
        text-gray-700 dark:text-gray-300
        bg-transparent
        ${className}
      `}
      style={{
        color: 'inherit',
        backgroundColor: 'transparent'
      }}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
