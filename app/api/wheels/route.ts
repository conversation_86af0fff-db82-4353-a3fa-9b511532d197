import { NextResponse } from 'next/server';
import { getWheelList } from '@/app/data/presetWheels';

export async function GET() {
  try {
    const wheelList = getWheelList();
    return NextResponse.json(wheelList);
  } catch (error) {
    console.error('Error fetching wheel list:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wheel list' },
      { status: 500 }
    );
  }
}
