import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { findWheelBySlug, presetWheels } from '@/app/data/presetWheels';
import { getServerWheelPageData } from '@/app/utils/server-api';
import WheelPageServer from './WheelPageServer';

interface WheelPageProps {
  params: Promise<{ slug: string }>;
}

// 生成动态元数据
export async function generateMetadata({ params }: WheelPageProps): Promise<Metadata> {
  const { slug } = await params;
  const wheel = findWheelBySlug(slug);

  if (!wheel) {
    return {
      title: 'Wheel Not Found - Decisions Maker Online',
      description: 'The requested decision wheel could not be found.',
    };
  }

  return {
    title: wheel.seo.title,
    description: wheel.seo.description,
    keywords: wheel.seo.keywords?.join(', '),
    openGraph: {
      title: wheel.seo.title,
      description: wheel.seo.description,
      type: 'website',
      url: `https://yourdomain.com/${slug}`,
    },
    twitter: {
      card: 'summary',
      title: wheel.seo.title,
      description: wheel.seo.description,
    },
    alternates: {
      canonical: `https://yourdomain.com/${slug}`,
    },
  };
}

// 选项1：使用静态生成 + ISR（推荐，SEO友好）
export async function generateStaticParams() {
  return presetWheels.map((wheel) => ({
    slug: wheel.slug,
  }));
}

// 启用增量静态再生，每60秒重新验证
export const revalidate = 60;

// 选项2：使用动态渲染（每次请求都获取最新数据）
// 如果要使用动态渲染，请注释掉上面的 generateStaticParams 和 revalidate，然后取消注释下面这行：
// export const dynamic = 'force-dynamic';

// 服务端渲染页面
export default async function WheelPage({ params }: WheelPageProps) {
  const { slug } = await params;

  try {
    // 通过API获取数据
    const { wheel, otherWheels } = await getServerWheelPageData(slug);

    return (
      <WheelPageServer
        wheel={wheel}
        otherWheels={otherWheels}
      />
    );
  } catch (error) {
    console.error('Error loading wheel page:', error);
    notFound();
  }
}
