/**
 * 重新验证页面缓存
 * POST /api/revalidate
 */

import { handleServerError } from '~/utils/server-api'

export default defineEventHandler(async (event) => {
  try {
    // 检查请求方法
    if (getMethod(event) !== 'POST') {
      setResponseStatus(event, 405)
      return {
        success: false,
        message: 'Method not allowed'
      }
    }

    // 获取查询参数
    const query = getQuery(event)
    const path = query.path as string

    // 在 Nuxt.js 中，我们可以使用 Nitro 的缓存清除功能
    // 这里我们模拟重新验证的过程
    
    if (path) {
      // 清除特定路径的缓存
      console.log(`Revalidating path: ${path}`)
      
      // 在实际应用中，这里可以调用 Nitro 的缓存清除 API
      // 或者触发静态生成的重新构建
      
      return {
        success: true,
        message: `Path ${path} revalidated successfully`,
        revalidated: true,
        path
      }
    } else {
      // 清除所有缓存
      console.log('Revalidating all paths')
      
      return {
        success: true,
        message: 'All paths revalidated successfully',
        revalidated: true
      }
    }
  } catch (error) {
    console.error('Error in revalidate API:', error)
    
    const errorResponse = handleServerError(error, 'revalidate API')
    
    setResponseStatus(event, errorResponse.statusCode)
    
    return {
      success: false,
      message: errorResponse.message,
      revalidated: false
    }
  }
})
