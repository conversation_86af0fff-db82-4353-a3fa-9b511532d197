<template>
  <div class="wheel-component">
    <!-- 设置按钮 -->
    <button
      v-if="!settings.isFullscreen"
      @click="settings.toggleSettings"
      :class="styles.settingsButton"
      title="Settings"
      aria-label="Open settings"
    >
      ⚙️
    </button>

    <!-- 主轮盘区域 -->
    <div 
      :class="[
        'relative',
        settings.settings.isFullscreen ? styles.fullscreen : ''
      ]"
    >
      <!-- 全屏模式下的标题 -->
      <h1 
        v-if="settings.settings.isFullscreen && title"
        class="text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100"
      >
        {{ title }}
      </h1>

      <!-- 轮盘容器 -->
      <div class="flex-1 flex items-center justify-center p-4">
        <div class="relative">
          <div
            :class="styles.container"
            :style="{
              '--wheel-size': `${responsive.wheelSize.value}px`
            }"
          >
            <!-- 指针 -->
            <div :class="styles.indicator" />
            
            <!-- 轮盘包装器 -->
            <div :class="styles.wheelWrapper">
              <!-- 轮盘画布 -->
              <canvas
                ref="canvasRef"
                :class="[
                  styles.wheel,
                  wheelState.state.rotating ? styles.spinning : ''
                ]"
                :style="{
                  transform: `rotate(${wheelState.state.currentRotation}deg)`
                }"
                @click="handleWheelClick"
              />

              <!-- 开始按钮 -->
              <button
                :class="styles.startButton"
                :style="{
                  '--start-button-size': `${responsive.startButtonSize.value}px`
                }"
                @click="startRotation"
                :disabled="wheelState.state.rotating || prizes.length === 0"
              >
                {{ wheelState.state.rotating ? 'Spinning...' : 'Start' }}
              </button>
            </div>
          </div>

          <!-- 结果显示 -->
          <div
            v-if="wheelState.state.result"
            :class="styles.result"
            :style="{ color: wheelState.state.result.color }"
          >
            🎉 {{ wheelState.state.result.text }}
          </div>
        </div>
      </div>

      <!-- 其他转盘链接 -->
      <div 
        v-if="otherWheels && otherWheels.length > 0 && !settings.settings.isFullscreen"
        class="mt-8 max-w-4xl mx-auto px-4"
      >
        <h2 class="text-xl font-semibold text-center mb-4 text-gray-900 dark:text-gray-100">
          Try Other Decision Wheels
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <NuxtLink
            v-for="wheel in otherWheels.slice(0, 6)"
            :key="wheel.id"
            :to="`/${wheel.slug}`"
            class="block p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600"
          >
            <h3 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
              {{ wheel.name }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {{ wheel.description }}
            </p>
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- 设置面板 -->
    <div
      v-if="settings.settings.showSettings"
      :class="[styles.settingsPanel, 'open']"
    >
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Settings
          </h3>
          <button
            @click="settings.toggleSettings"
            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        <!-- 轮盘大小调节 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Wheel Size: {{ responsive.wheelSize.value }}px
          </label>
          <input
            type="range"
            min="200"
            max="600"
            step="50"
            :value="responsive.wheelSize.value"
            @input="handleWheelSizeChange"
            class="w-full"
          />
        </div>

        <!-- 全屏切换 -->
        <div class="mb-6">
          <button
            @click="settings.toggleFullscreen"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {{ settings.settings.isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen' }}
          </button>
        </div>

        <!-- 奖品编辑 -->
        <div class="mb-6">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Edit Prizes
          </h4>
          <div class="space-y-2 max-h-60 overflow-y-auto">
            <div
              v-for="(prize, index) in prizes"
              :key="index"
              class="flex items-center space-x-2"
            >
              <input
                v-model="prize.text"
                type="text"
                class="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                @input="updatePrizes"
              />
              <input
                v-model="prize.color"
                type="color"
                class="w-8 h-8 border border-gray-300 dark:border-gray-600 rounded cursor-pointer"
                @input="updatePrizes"
              />
              <button
                @click="removePrize(index)"
                class="text-red-500 hover:text-red-700 text-sm"
                :disabled="prizes.length <= 2"
              >
                🗑️
              </button>
            </div>
          </div>
          <button
            @click="addPrize"
            class="mt-2 w-full px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            Add Prize
          </button>
        </div>

        <!-- 重置按钮 -->
        <button
          @click="resetWheel"
          class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          Reset Wheel
        </button>
      </div>
    </div>

    <!-- 设置面板遮罩 -->
    <div
      v-if="settings.settings.showSettings"
      class="fixed inset-0 bg-black bg-opacity-50 z-40"
      @click="settings.toggleSettings"
    />
  </div>
</template>

<script setup lang="ts">
import type { Prize, WheelListItem } from '~/types'
import styles from './wheel.module.css'

interface Props {
  prizes: Prize[]
  onPrizesChange?: (prizes: Prize[]) => void
  wheelId?: string
  title?: string
  otherWheels?: WheelListItem[]
}

const props = withDefaults(defineProps<Props>(), {
  prizes: () => [],
  otherWheels: () => []
})

const emit = defineEmits<{
  prizesChange: [prizes: Prize[]]
}>()

// 组合式函数
const wheelState = useWheelState(props.prizes)
const settings = useWheelSettings()
const responsive = useWheelResponsive()

// 响应式数据
const canvasRef = ref<HTMLCanvasElement>()
const prizes = ref<Prize[]>([...props.prizes])

// 监听 props 变化
watch(() => props.prizes, (newPrizes) => {
  prizes.value = [...newPrizes]
  wheelState.updatePrizes(newPrizes)
  nextTick(() => {
    drawWheel()
  })
}, { immediate: true })

// 绘制轮盘
const drawWheel = () => {
  if (!canvasRef.value || prizes.value.length === 0) return

  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const size = responsive.wheelSize.value
  canvas.width = size
  canvas.height = size

  const centerX = size / 2
  const centerY = size / 2
  const radius = size / 2 - 10

  const sectorAngle = (2 * Math.PI) / prizes.value.length

  prizes.value.forEach((prize, index) => {
    const startAngle = index * sectorAngle - Math.PI / 2
    const endAngle = startAngle + sectorAngle

    // 绘制扇形
    ctx.beginPath()
    ctx.moveTo(centerX, centerY)
    ctx.arc(centerX, centerY, radius, startAngle, endAngle)
    ctx.closePath()
    ctx.fillStyle = prize.color
    ctx.fill()
    ctx.strokeStyle = '#ffffff'
    ctx.lineWidth = 2
    ctx.stroke()

    // 绘制文字
    ctx.save()
    ctx.translate(centerX, centerY)
    ctx.rotate(startAngle + sectorAngle / 2)
    ctx.textAlign = 'center'
    ctx.fillStyle = getContrastColor(prize.color)
    ctx.font = `${Math.max(12, size / 25)}px Arial`
    ctx.fillText(prize.text, radius * 0.7, 5)
    ctx.restore()
  })
}

// 获取对比色
const getContrastColor = (hexColor: string): string => {
  const r = parseInt(hexColor.slice(1, 3), 16)
  const g = parseInt(hexColor.slice(3, 5), 16)
  const b = parseInt(hexColor.slice(5, 7), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness > 128 ? '#000000' : '#ffffff'
}

// 开始旋转
const startRotation = () => {
  wheelState.startRotation()
}

// 处理轮盘点击
const handleWheelClick = () => {
  if (!wheelState.state.rotating) {
    startRotation()
  }
}

// 更新奖品
const updatePrizes = () => {
  wheelState.updatePrizes(prizes.value)
  emit('prizesChange', prizes.value)
  props.onPrizesChange?.(prizes.value)
  nextTick(() => {
    drawWheel()
  })
}

// 添加奖品
const addPrize = () => {
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3']
  const randomColor = colors[Math.floor(Math.random() * colors.length)]
  
  prizes.value.push({
    text: `Option ${prizes.value.length + 1}`,
    color: randomColor
  })
  updatePrizes()
}

// 删除奖品
const removePrize = (index: number) => {
  if (prizes.value.length > 2) {
    prizes.value.splice(index, 1)
    updatePrizes()
  }
}

// 重置轮盘
const resetWheel = () => {
  wheelState.resetWheel()
  prizes.value = [...props.prizes]
  updatePrizes()
}

// 处理轮盘大小变化
const handleWheelSizeChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  settings.updateWheelSize(parseInt(target.value))
  nextTick(() => {
    drawWheel()
  })
}

// 生命周期
onMounted(() => {
  drawWheel()
})

// 监听响应式变化
watch(() => responsive.wheelSize.value, () => {
  nextTick(() => {
    drawWheel()
  })
})
</script>

<style scoped>
.wheel-component {
  position: relative;
  width: 100%;
  height: 100%;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
