/**
 * 客户端API工具函数
 */

import type { WheelPageResponse, PresetWheel, WheelListItem } from '~/types'
import { api, handleApiError } from '~/config/api'

/**
 * 获取转盘页面数据（客户端）
 */
export async function getWheelPageData(slug?: string): Promise<{
  wheel: PresetWheel;
  otherWheels: WheelListItem[];
}> {
  try {
    const endpoint = slug ? `/wheel-page/${slug}` : '/wheel-page'
    const response = await api.get<WheelPageResponse>(endpoint)
    
    if (!response.success || !response.currentWheel) {
      throw new Error(response.message || 'Failed to fetch wheel data')
    }
    
    return {
      wheel: response.currentWheel,
      otherWheels: response.otherWheels
    }
  } catch (error) {
    console.error('Error fetching wheel page data:', error)
    throw new Error(handleApiError(error))
  }
}

/**
 * 获取默认转盘数据（客户端）
 */
export async function getDefaultWheelData(): Promise<{
  defaultWheel: PresetWheel;
  otherWheels: WheelListItem[];
}> {
  try {
    const response = await api.get<WheelPageResponse>('/wheel-page')
    
    if (!response.success || !response.currentWheel) {
      throw new Error(response.message || 'Failed to fetch default wheel data')
    }
    
    return {
      defaultWheel: response.currentWheel,
      otherWheels: response.otherWheels
    }
  } catch (error) {
    console.error('Error fetching default wheel data:', error)
    throw new Error(handleApiError(error))
  }
}

/**
 * 获取转盘列表
 */
export async function getWheelListData(): Promise<WheelListItem[]> {
  try {
    const response = await api.get<WheelListItem[]>('/wheel-list')
    return response
  } catch (error) {
    console.error('Error fetching wheel list:', error)
    throw new Error(handleApiError(error))
  }
}

/**
 * 获取默认奖品
 */
export async function getDefaultPrizes() {
  try {
    const response = await api.get('/prizes')
    return response
  } catch (error) {
    console.error('Error fetching default prizes:', error)
    throw new Error(handleApiError(error))
  }
}

/**
 * 重新验证页面
 */
export async function revalidatePage(path?: string) {
  try {
    const endpoint = path ? `/revalidate?path=${encodeURIComponent(path)}` : '/revalidate'
    const response = await api.post(endpoint)
    return response
  } catch (error) {
    console.error('Error revalidating page:', error)
    throw new Error(handleApiError(error))
  }
}

/**
 * 通用错误处理
 */
export function createErrorHandler(context: string) {
  return (error: any) => {
    console.error(`Error in ${context}:`, error)
    
    // 可以在这里添加错误上报逻辑
    // reportError(error, context)
    
    return {
      error: true,
      message: handleApiError(error)
    }
  }
}

/**
 * 创建带重试的API请求
 */
export function createRetryableRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
) {
  return async (): Promise<T> => {
    let lastError: any
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error
        
        if (i === maxRetries) {
          throw error
        }
        
        // 指数退避
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
      }
    }
    
    throw lastError
  }
}

/**
 * 缓存API响应
 */
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

export function createCachedRequest<T>(
  key: string,
  requestFn: () => Promise<T>,
  ttl: number = 5 * 60 * 1000 // 5分钟
): () => Promise<T> {
  return async (): Promise<T> => {
    const now = Date.now()
    const cached = cache.get(key)
    
    // 检查缓存是否有效
    if (cached && (now - cached.timestamp) < cached.ttl) {
      return cached.data
    }
    
    // 获取新数据
    const data = await requestFn()
    
    // 更新缓存
    cache.set(key, {
      data,
      timestamp: now,
      ttl
    })
    
    return data
  }
}

/**
 * 清除缓存
 */
export function clearCache(key?: string) {
  if (key) {
    cache.delete(key)
  } else {
    cache.clear()
  }
}

/**
 * 批量请求
 */
export async function batchRequests<T>(
  requests: Array<() => Promise<T>>,
  concurrency: number = 3
): Promise<T[]> {
  const results: T[] = []
  
  for (let i = 0; i < requests.length; i += concurrency) {
    const batch = requests.slice(i, i + concurrency)
    const batchResults = await Promise.all(batch.map(request => request()))
    results.push(...batchResults)
  }
  
  return results
}
