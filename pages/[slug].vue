<template>
  <div>
    <!-- 页面头部 -->
    <AppHeader />
    
    <!-- 主要内容 -->
    <div class="min-h-screen py-4 bg-white dark:bg-gray-900 overflow-hidden" id="wheelContainer">
      <div class="h-full flex flex-col items-center max-w-full">
        <h1 class="text-2xl md:text-3xl font-bold text-center mb-4 text-gray-900 dark:text-gray-100 flex-shrink-0">
          {{ wheel.name }}
        </h1>
        <div class="flex flex-col items-center flex-grow w-full">
          <!-- 交互式转盘组件 -->
          <WheelInteractive
            :initial-prizes="wheel.prizes"
            :wheel-id="wheel.slug"
            :title="wheel.name"
            :other-wheels="otherWheels"
          />
        </div>
      </div>
      
      <!-- 文章内容 -->
      <div 
        v-if="wheel.articles && wheel.articles.length > 0"
        class="max-w-4xl mx-auto px-4 mt-12"
      >
        <div class="grid gap-8 md:grid-cols-2">
          <article
            v-for="(article, index) in wheel.articles"
            :key="index"
            class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700"
          >
            <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
              {{ article.title }}
            </h2>
            <div 
              class="prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300"
              v-html="article.content"
            />
          </article>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { WheelPageResponse } from '~/types'
import { getServerWheelPageData, getWheelSeoData, validateWheelSlug } from '~/utils/server-api'

// 获取路由参数
const route = useRoute()
const slug = route.params.slug as string

// 验证 slug 是否存在
if (!validateWheelSlug(slug)) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Wheel not found'
  })
}

// 服务端数据获取
const { data } = await useFetch<WheelPageResponse>(`/api/wheel-page/${slug}`, {
  server: true,
  default: () => ({
    currentWheel: null,
    otherWheels: [],
    success: false
  })
})

// 如果数据获取失败，使用服务端备用数据
if (!data.value.success || !data.value.currentWheel) {
  try {
    const serverData = await getServerWheelPageData(slug)
    data.value = {
      currentWheel: serverData.wheel,
      otherWheels: serverData.otherWheels,
      success: true,
      message: 'Data loaded from server'
    }
  } catch (error) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Wheel not found'
    })
  }
}

// 解构数据
const wheel = computed(() => data.value.currentWheel!)
const otherWheels = computed(() => data.value.otherWheels)

// SEO 配置
const seoData = getWheelSeoData(slug)

useSeoMeta({
  title: seoData?.title || wheel.value.seo.title,
  description: seoData?.description || wheel.value.seo.description,
  keywords: seoData?.keywords || wheel.value.seo.keywords.join(', '),
  ogTitle: seoData?.title || wheel.value.seo.title,
  ogDescription: seoData?.description || wheel.value.seo.description,
  ogImage: seoData?.ogImage || 'https://decisionsmaker.online/og-image.png',
  ogUrl: seoData?.canonical || `https://decisionsmaker.online/${slug}`,
  twitterCard: 'summary_large_image',
  twitterTitle: seoData?.title || wheel.value.seo.title,
  twitterDescription: seoData?.description || wheel.value.seo.description,
})

useHead({
  link: [
    {
      rel: 'canonical',
      href: seoData?.canonical || `https://decisionsmaker.online/${slug}`
    }
  ]
})

// 结构化数据
useJsonld({
  '@context': 'https://schema.org',
  '@type': 'WebApplication',
  name: wheel.value.name,
  description: wheel.value.seo.description,
  url: `https://decisionsmaker.online/${slug}`,
  applicationCategory: 'UtilityApplication',
  operatingSystem: 'Any',
  offers: {
    '@type': 'Offer',
    price: '0',
    priceCurrency: 'USD'
  },
  creator: {
    '@type': 'Organization',
    name: 'DecisionsMaker Online'
  }
})

// 页面配置
definePageMeta({
  layout: 'default'
})

// 预渲染配置（用于 ISR）
export const prerender = true

// 生成静态路由
export async function generateStaticParams() {
  const { presetWheels } = await import('~/data/presetWheels')
  return presetWheels.map((wheel) => ({
    slug: wheel.slug,
  }))
}
</script>

<style scoped>
/* 页面特定样式 */
.prose {
  color: inherit;
}

.prose p {
  margin-bottom: 1rem;
}

.prose ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  h1 {
    font-size: 1.5rem;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
}

/* 深色模式下的文章样式 */
.dark .prose {
  color: #d1d5db;
}

.dark .prose h2 {
  color: #f9fafb;
}

/* 加载状态 */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* 错误状态 */
.error {
  color: #ef4444;
  text-align: center;
  padding: 2rem;
}
</style>
