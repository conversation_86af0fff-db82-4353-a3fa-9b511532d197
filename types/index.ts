// 轮盘奖品类型
export interface Prize {
  text: string;
  color: string;
}

// SEO配置类型
export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
}

// 文章类型
export interface Article {
  title: string;
  content: string;
}

// 预设轮盘类型
export interface PresetWheel {
  id: string;
  name: string;
  slug: string;
  isDefault?: boolean;
  prizes: Prize[];
  seo: SEOConfig;
  articles?: Article[];
}

// 轮盘列表项类型
export interface WheelListItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
}

// API响应类型
export interface WheelPageResponse {
  currentWheel: PresetWheel | null;
  otherWheels: WheelListItem[];
  success: boolean;
  message?: string;
}

// 环境配置类型
export interface EnvironmentConfig {
  apiBaseUrl: string;
  isDevelopment: boolean;
  isProduction: boolean;
}

// 主题类型
export type Theme = 'light' | 'dark' | 'system';

// 颜色模式上下文类型
export interface ColorModeContext {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: 'light' | 'dark';
}

// 轮盘交互组件属性类型
export interface WheelInteractiveProps {
  initialPrizes: Prize[];
  wheelId?: string;
  title?: string;
}

// 页面属性类型
export interface WheelPageProps {
  params: Promise<{ slug: string }>;
}

// 服务端组件属性类型
export interface WheelPageServerProps {
  wheel: PresetWheel;
  otherWheels: WheelListItem[];
}

export interface HomePageServerProps {
  defaultWheel: PresetWheel;
  otherWheels: WheelListItem[];
}

// API端点配置类型
export interface APIEndpoints {
  wheelPage: (slug?: string) => string;
}

// 请求配置类型
export interface RequestConfig {
  defaultHeaders: Record<string, string>;
  timeout: number;
  retryCount: number;
}

// 轮盘样式配置类型
export interface WheelStyleConfig {
  wheelSize: number;
  startButtonSize: number;
  colors: string[];
  fontSize: number;
}

// 动画配置类型
export interface AnimationConfig {
  duration: number;
  easing: string;
  extraSpins: number;
}

// 轮盘状态类型
export interface WheelState {
  rotating: boolean;
  currentRotation: number;
  result: Prize | null;
  prizes: Prize[];
}

// 模态框状态类型
export interface ModalState {
  isOpen: boolean;
  title?: string;
  content?: string;
}

// 设置面板状态类型
export interface SettingsState {
  showSettings: boolean;
  isFullscreen: boolean;
  wheelSize: number;
}

// 导航项类型
export interface NavigationItem {
  name: string;
  href: string;
  icon?: string;
}

// 页面元数据类型
export interface PageMeta {
  title: string;
  description: string;
  keywords?: string;
  ogImage?: string;
  canonical?: string;
}

// 错误类型
export interface AppError {
  message: string;
  code?: string | number;
  details?: any;
}

// 加载状态类型
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

// 用户偏好设置类型
export interface UserPreferences {
  theme: Theme;
  language: string;
  animations: boolean;
  sound: boolean;
}

// 分析事件类型
export interface AnalyticsEvent {
  name: string;
  parameters?: Record<string, any>;
}

// 路由参数类型
export interface RouteParams {
  slug?: string;
  [key: string]: string | undefined;
}

// 查询参数类型
export interface QueryParams {
  [key: string]: string | string[] | undefined;
}

// 响应式断点类型
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 浏览器信息类型
export interface BrowserInfo {
  name: string;
  version: string;
  platform: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}
