import { NextRequest, NextResponse } from 'next/server';
import { findWheelBySlug } from '@/app/data/presetWheels';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const wheel = findWheelBySlug(slug);
    
    if (!wheel) {
      return NextResponse.json(
        { error: 'Wheel not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(wheel);
  } catch (error) {
    console.error('Error fetching wheel:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wheel' },
      { status: 500 }
    );
  }
}
