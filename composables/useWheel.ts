/**
 * 轮盘相关的组合式函数
 */

import type { Prize, WheelState, AnimationConfig } from '~/types'

/**
 * 轮盘状态管理
 */
export function useWheelState(initialPrizes: Prize[] = []) {
  const state = reactive<WheelState>({
    rotating: false,
    currentRotation: 0,
    result: null,
    prizes: [...initialPrizes]
  })

  // 更新奖品
  const updatePrizes = (newPrizes: Prize[]) => {
    state.prizes = [...newPrizes]
    state.result = null
  }

  // 重置轮盘
  const resetWheel = () => {
    state.rotating = false
    state.currentRotation = 0
    state.result = null
  }

  // 开始旋转
  const startRotation = () => {
    if (state.rotating || state.prizes.length === 0) return

    state.rotating = true
    state.result = null

    // 随机选择一个奖品
    const selectedIndex = Math.floor(Math.random() * state.prizes.length)
    const selectedPrize = state.prizes[selectedIndex]

    // 计算旋转角度
    const extraSpins = 8 + Math.random() * 3 // 随机旋转8-11圈
    const sectorAngle = 360 / state.prizes.length
    const wholePart = Math.floor(extraSpins)
    const decimalPart = extraSpins - wholePart
    const decimalPartAngle = sectorAngle / 2 - sectorAngle * decimalPart
    const selectedAngleFromRight = -selectedIndex * sectorAngle + decimalPartAngle
    const targetAngle = 360 * wholePart + selectedAngleFromRight

    // 执行动画
    animateRotation(targetAngle, selectedPrize)
  }

  // 动画函数
  const animateRotation = (targetAngle: number, selectedPrize: Prize) => {
    const duration = 8000 // 8秒
    let start: number | null = null

    const animate = (timestamp: number) => {
      if (!start) start = timestamp
      const progress = (timestamp - start) / duration

      if (progress < 1) {
        const currentAngle = easeOut(progress) * targetAngle
        state.currentRotation = currentAngle
        requestAnimationFrame(animate)
      } else {
        state.currentRotation = targetAngle % 360
        state.rotating = false
        state.result = selectedPrize
      }
    }

    requestAnimationFrame(animate)
  }

  // 缓动函数
  const easeOut = (x: number): number => {
    return 1 - Math.pow(1 - x, 3)
  }

  return {
    state: readonly(state),
    updatePrizes,
    resetWheel,
    startRotation
  }
}

/**
 * 轮盘设置管理
 */
export function useWheelSettings() {
  const settings = reactive({
    showSettings: false,
    isFullscreen: false,
    wheelSize: 400,
    enableSound: true,
    enableAnimation: true,
    animationDuration: 8000
  })

  // 切换设置面板
  const toggleSettings = () => {
    settings.showSettings = !settings.showSettings
  }

  // 切换全屏
  const toggleFullscreen = async () => {
    if (!document.fullscreenElement) {
      await document.documentElement.requestFullscreen()
      settings.isFullscreen = true
    } else {
      await document.exitFullscreen()
      settings.isFullscreen = false
    }
  }

  // 更新轮盘大小
  const updateWheelSize = (size: number) => {
    settings.wheelSize = Math.max(200, Math.min(600, size))
  }

  // 监听全屏变化
  onMounted(() => {
    const handleFullscreenChange = () => {
      settings.isFullscreen = !!document.fullscreenElement
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)

    onUnmounted(() => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    })
  })

  return {
    settings: readonly(settings),
    toggleSettings,
    toggleFullscreen,
    updateWheelSize
  }
}

/**
 * 轮盘数据获取
 */
export function useWheelData() {
  const { $fetch } = useNuxtApp()

  // 获取轮盘页面数据
  const fetchWheelData = async (slug?: string) => {
    try {
      const endpoint = slug ? `/api/wheel-page/${slug}` : '/api/wheel-page'
      const response = await $fetch(endpoint)
      return response
    } catch (error) {
      console.error('Error fetching wheel data:', error)
      throw error
    }
  }

  // 获取转盘列表
  const fetchWheelList = async () => {
    try {
      const response = await $fetch('/api/wheel-list')
      return response
    } catch (error) {
      console.error('Error fetching wheel list:', error)
      throw error
    }
  }

  return {
    fetchWheelData,
    fetchWheelList
  }
}

/**
 * 轮盘动画配置
 */
export function useWheelAnimation() {
  const config = reactive<AnimationConfig>({
    duration: 8000,
    easing: 'cubic-bezier(0.23, 1, 0.32, 1)',
    extraSpins: 8
  })

  // 更新动画配置
  const updateConfig = (newConfig: Partial<AnimationConfig>) => {
    Object.assign(config, newConfig)
  }

  // 获取CSS动画属性
  const getAnimationStyle = (rotation: number) => {
    return {
      transform: `rotate(${rotation}deg)`,
      transition: config.easing ? `transform ${config.duration}ms ${config.easing}` : 'none'
    }
  }

  return {
    config: readonly(config),
    updateConfig,
    getAnimationStyle
  }
}

/**
 * 轮盘响应式设计
 */
export function useWheelResponsive() {
  const windowWidth = ref(0)
  const windowHeight = ref(0)

  // 计算轮盘大小
  const wheelSize = computed(() => {
    const minSize = 200
    const maxSize = 500
    const padding = 40

    if (process.client) {
      const availableWidth = windowWidth.value - padding * 2
      const availableHeight = windowHeight.value - padding * 2
      const availableSize = Math.min(availableWidth, availableHeight)
      
      return Math.max(minSize, Math.min(maxSize, availableSize))
    }

    return 400 // 默认大小
  })

  // 计算开始按钮大小
  const startButtonSize = computed(() => {
    return Math.max(60, wheelSize.value * 0.15)
  })

  // 更新窗口尺寸
  const updateWindowSize = () => {
    if (process.client) {
      windowWidth.value = window.innerWidth
      windowHeight.value = window.innerHeight
    }
  }

  // 监听窗口大小变化
  onMounted(() => {
    updateWindowSize()
    window.addEventListener('resize', updateWindowSize)

    onUnmounted(() => {
      window.removeEventListener('resize', updateWindowSize)
    })
  })

  return {
    wheelSize,
    startButtonSize,
    windowWidth: readonly(windowWidth),
    windowHeight: readonly(windowHeight)
  }
}
