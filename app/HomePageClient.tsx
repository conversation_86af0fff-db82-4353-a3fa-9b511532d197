"use client";
import LuckWheel from '@/app/components/wheel/wheel';
import { Prize, WheelListItem } from '@/app/components/wheel/types';
import { getWheelPageData } from '@/app/utils/api';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';

// 默认奖品列表作为备用
const defaultPrizes: Prize[] = [
  { text: "Yes", color: "#3b82f6" },
  { text: "No", color: "#22c55e" },
];

// 文章内容接口类型
interface Article {
  title: string;
  content: string;
}

export default function HomePageClient() {
  const [prizes, setPrizes] = useState<Prize[]>([]);
  const [articles, setArticles] = useState<Article[]>([]);
  const [otherWheels, setOtherWheels] = useState<WheelListItem[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取选项数据
  useEffect(() => {
    const fetchPrizes = async () => {
      try {
        const response = await fetch('/api/prizes');
        if (!response.ok) {
          throw new Error('Failed to fetch prizes');
        }
        const data = await response.json();
        setPrizes(data);
      } catch (error) {
        console.error('Error fetching prizes:', error);
        // 使用默认奖品列表
        setPrizes(defaultPrizes);
      } finally {
        setLoading(false);
      }
    };

    fetchPrizes();
  }, []);

  // 获取文章数据
  useEffect(() => {
    const fetchArticles = async () => {
      try {
        const response = await fetch('/api/articles');
        if (!response.ok) {
          throw new Error('Failed to fetch articles');
        }
        const data = await response.json();
        setArticles(data);
      } catch (error) {
        console.error('Error fetching articles:', error);
      }
    };

    fetchArticles();
  }, []);

  // 获取其他转盘数据
  useEffect(() => {
    const fetchOtherWheels = async () => {
      try {
        // 使用统一接口获取首页数据（包含其他转盘列表）
        const data = await getWheelPageData();
        if (data.success) {
          setOtherWheels(data.otherWheels);
        }
      } catch (error) {
        console.error('Error fetching other wheels:', error);
      }
    };

    fetchOtherWheels();
  }, []);

  return (
    <>
      <header className="bg-white shadow">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex text-lg font-bold mr-20 items-center">
            <Image src="/logo.jpg" width={40} height={40} className="rounded mr-2" alt="Decisions Maker Online Logo" />
            <div>Decisions Maker Online</div>
          </div>
        </div>
      </header>
      <div className="min-h-screen py-8 bg-white" id="wheelContainer">
        <div className="h-full flex flex-col items-center justify-center">
          <h1 className="text-3xl font-bold text-center mb-2">Yes or No Decision Maker</h1>
          <div className="flex flex-col items-center">
            {loading ? (
              <div className="flex items-center justify-center h-[600px] w-[600px]">
                <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <LuckWheel 
                prizes={prizes} 
                onPrizesChange={setPrizes}
              />
            )}
          </div>
        </div>
      </div>

      {/* 其他转盘导航 */}
      {otherWheels.length > 0 && (
        <section className="max-w-6xl mx-auto mt-16 px-4">
          <h2 className="text-2xl font-bold mb-6 text-center">Try Other Decision Wheels</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {otherWheels.map((wheel) => (
              <Link 
                key={wheel.id} 
                href={`/${wheel.slug}`}
                className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow"
              >
                <h3 className="font-semibold mb-2 text-blue-600">{wheel.name}</h3>
                <p className="text-gray-600 text-sm">{wheel.description}</p>
              </Link>
            ))}
          </div>
        </section>
      )}

      <article className="max-w-4xl mx-auto mt-16 px-4">
        {articles.map((article, index) => (
          <section key={index} className="mb-12">
            <h2 className="text-2xl font-bold mb-6">{article.title}</h2>
            <div className="text-gray-700 leading-relaxed mb-4" 
                 dangerouslySetInnerHTML={{ __html: article.content }} />
          </section>
        ))}
      </article>
    </>
  );
}
