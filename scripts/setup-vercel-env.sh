#!/bin/bash

# Vercel环境变量设置脚本
# 使用前请确保已安装并登录Vercel CLI: npm i -g vercel && vercel login

echo "🚀 设置Vercel环境变量..."

# 从.env.production读取配置
if [ -f ".env.production" ]; then
    echo "📋 从.env.production读取配置..."
    
    # 读取API_BASE_URL
    API_BASE_URL=$(grep "NEXT_PUBLIC_API_BASE_URL" .env.production | cut -d '=' -f2)
    SITE_URL=$(grep "NEXT_PUBLIC_SITE_URL" .env.production | cut -d '=' -f2)
    
    echo "API Base URL: $API_BASE_URL"
    echo "Site URL: $SITE_URL"
    
    # 设置生产环境变量
    echo "🔧 设置生产环境变量..."
    echo "$API_BASE_URL" | vercel env add NEXT_PUBLIC_API_BASE_URL production
    echo "$SITE_URL" | vercel env add NEXT_PUBLIC_SITE_URL production
    
    # 设置预览环境变量
    echo "🔧 设置预览环境变量..."
    echo "$API_BASE_URL" | vercel env add NEXT_PUBLIC_API_BASE_URL preview
    echo "$SITE_URL" | vercel env add NEXT_PUBLIC_SITE_URL preview
    
    echo "✅ 环境变量设置完成！"
    echo "💡 现在可以重新部署项目了"
    
else
    echo "❌ 未找到.env.production文件"
    echo "请先创建.env.production文件并配置环境变量"
fi
