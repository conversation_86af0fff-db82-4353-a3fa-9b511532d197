import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { Analytics } from "@vercel/analytics/next"
import Script from "next/script";
import { ThemeProvider } from "./components/theme/ThemeProvider";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "DecisionsMaker Online - Smart Decision Making Wheel",
  description: "Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly. Perfect for personal and business decisions.",
  keywords: "decision making, decision wheel, random choice generator, decisionsmaker online, online decision tool, decision making tool",
  authors: [{ name: "DecisionsMaker Online" }],
  creator: "DecisionsMaker Online",
  publisher: "DecisionsMaker Online",
  robots: "index, follow",
  openGraph: {
    title: "DecisionsMaker Online - Smart Decision Making Wheel",
    description: "Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly.",
    url: "https://decisionsmaker.online",
    type: "website",
    siteName: "DecisionsMaker Online",
    images: [
      {
        url: "https://decisionsmaker.online/og-image.png",
        width: 1200,
        height: 630,
        alt: "DecisionsMaker Online Logo"
      }
    ]
  },
  twitter: {
    card: "summary_large_image",
    title: "DecisionsMaker Online - Smart Decision Making Wheel",
    description: "Make better decisions with DecisionsMaker Online. Our interactive decision wheel helps you choose among multiple options quickly and fairly.",
    site: "@decisionsmaker",
    creator: "@decisionsmaker"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors`}
      >
        <ThemeProvider>
          {children}
        </ThemeProvider>
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-36JS659442"
          strategy="afterInteractive"
        />
        <Script id="gtag-init" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-36JS659442');
          `}
        </Script>
        <Script id="baidu-analytics" strategy="afterInteractive">
          {`
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?b209671ea8fa33b06ef52e4025885a97";
              var s = document.getElementsByTagName("script")[0]; 
              s.parentNode.insertBefore(hm, s);
            })();
          `}
        </Script>
      </body>
      <Analytics/>
    </html>
  );
}
