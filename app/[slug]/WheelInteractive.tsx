"use client";
import { useState, useEffect } from 'react';
import LuckWheel from '@/app/components/wheel/wheel';
import { Prize } from '@/app/components/wheel/types';

interface WheelInteractiveProps {
  initialPrizes: Prize[];
  wheelId?: string;
  title?: string;
}

// 只负责转盘交互的客户端组件
export default function WheelInteractive({ initialPrizes, wheelId, title }: WheelInteractiveProps) {
  const [prizes, setPrizes] = useState<Prize[]>(initialPrizes);

  // 当 initialPrizes 改变时，更新本地状态
  useEffect(() => {
    setPrizes(initialPrizes);
  }, [initialPrizes]);

  return (
    <LuckWheel
      prizes={prizes}
      onPrizesChange={setPrizes}
      wheelId={wheelId}
      title={title}
    />
  );
}
