import { useEffect } from 'react';
import { Prize } from '../wheel/types';
import Image from 'next/image';

export default function SelectedModal({ 
  showModal = true,
  onClose,
  result = null
}: {
  showModal: boolean;
  result: Prize | null;
  onClose?: () => void;
}) {

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose?.();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  const closeModal = () => {
    onClose?.(); // 通过回调函数通知父组件更新状态
  }
  return (
   <>
    <div className={`fixed inset-0 z-50 overflow-y-auto transition-opacity duration-300 ease-in-out ${showModal ? 'opacity-100' : 'invisible opacity-0'}`}>
      <div className="flex min-h-full items-center justify-center p-4 text-center">
        <div className="fixed inset-0 bg-black bg-opacity-30 dark:bg-black dark:bg-opacity-50 transition-opacity" onClick={closeModal} />
        <div className={`relative h-[400px] transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg border border-gray-200 dark:border-gray-700 ${showModal ? 'opacity-100' : 'invisible opacity-0'}`}>
          <div className="bg-white dark:bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div className="flex flex-col items-center justify-center h-[300px]">
              {result?.image && result.image.trim() !== '' && (
                <Image
                  src={result.image}
                  className="rounded mb-4"
                  alt="selected image"
                  width={150}
                  height={150}
                />
              )}
              <h1 className="text-2xl font-semibold text-center leading-6 text-gray-900 dark:text-gray-100">{result?.text}</h1>
            </div>
          </div>
          <div className="absolute bottom-2 w-full py-3 sm:flex sm:flex-row-reverse sm:px-6 justify-center">
            <button
              type="button"
              className="inline-flex w-full justify-center rounded-md border-gray-900 dark:border-gray-300 border-2 px-2 py-1 text-sm font-semibold shadow-sm sm:w-auto text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              onClick={closeModal}
            >
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
   </>
  );
}