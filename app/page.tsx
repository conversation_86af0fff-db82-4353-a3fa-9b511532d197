import { Metadata } from 'next';
import { getServerDefaultWheel } from '@/app/utils/server-api';
import HomePageServer from './HomePageServer';

export const metadata: Metadata = {
  title: 'Yes or No Decision Maker - Quick Binary Choice Wheel | DecisionsMaker Online',
  description: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices, quick decisions, and breaking ties. Free online decision maker tool.',
  keywords: 'yes no decision, binary choice, decision maker, quick decision, decision wheel, random choice',
  openGraph: {
    title: 'Yes or No Decision Maker - Quick Binary Choice Wheel',
    description: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices and quick decisions.',
    type: 'website',
    url: 'https://yourdomain.com',
  },
  twitter: {
    card: 'summary',
    title: 'Yes or No Decision Maker - Quick Binary Choice Wheel',
    description: 'Make quick yes or no decisions with our simple decision wheel. Perfect for binary choices and quick decisions.',
  },
  alternates: {
    canonical: 'https://yourdomain.com',
  },
};

// 启用增量静态再生，每60秒重新验证
export const revalidate = 60;

// 服务端渲染主页
export default async function Home() {
  // 通过API获取数据
  const { defaultWheel, otherWheels } = await getServerDefaultWheel();

  return (
    <HomePageServer
      defaultWheel={defaultWheel}
      otherWheels={otherWheels}
    />
  );
}
