/**
 * 生成 robots.txt
 * GET /api/robots.txt
 */

import { generateRobotsContent } from '~/utils/server-api'

export default defineEventHandler(async (event) => {
  try {
    // 设置响应头
    setHeader(event, 'Content-Type', 'text/plain')
    setHeader(event, 'Cache-Control', 'public, max-age=86400, s-maxage=86400') // 24小时缓存

    // 生成 robots.txt 内容
    const robotsContent = generateRobotsContent()

    return robotsContent
  } catch (error) {
    console.error('Error generating robots.txt:', error)
    
    setResponseStatus(event, 500)
    
    return 'Error generating robots.txt'
  }
})
